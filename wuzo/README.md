# Wuzo - 手工木雕创作与分享社区

## 项目概述

Wuzo 是一个专为木雕爱好者设计的AI驱动创作与分享社区，致力于帮助用户从创作到展示整个木雕过程。平台提供完整的作品发布功能，并通过专业AI助手为用户提供雕刻指导，打造一个集学习、创作、分享于一体的木雕艺术平台。

## 目标用户

- **木雕初学者**：希望掌握基础木雕技法的用户
- **木雕爱好者**：对雕刻有一定经验，希望提升技艺的用户  
- **艺术创作者**：使用木雕作为创作材料的艺术家
- **教育工作者**：需要通过木雕创作来进行教学的教师

## 应用功能详解

### 🎬 主页视频流
- **竖版视频浏览**：类似抖音的全屏视频浏览体验
- **木雕作品展示**：精选的木雕创作视频，包含不同技法和风格
- **智能推荐**：基于用户喜好推荐相关作品
- **点赞互动**：支持作品点赞，点赞状态在主页和详情页同步
- **作品详情**：点击可查看作品详细信息和创作者资料
- **内容过滤**：支持屏蔽不感兴趣的内容和举报功能

### 🤖 AI智能助手
- **专业指导**：集成Moonshot AI，提供专业的木雕创作指导
- **实时对话**：支持文字对话，获取雕刻技巧、工具选择、材料建议
- **历史记录**：自动保存聊天记录，方便回顾学习内容
- **Markdown支持**：支持富文本消息显示，包括代码块、列表等
- **离线知识**：网络异常时提供基础木雕知识和建议
- **清空功能**：支持清空聊天历史，重新开始对话

### 📱 作品发布系统
- **视频上传**：支持相机录制和相册选择，最大支持3分钟视频
- **完整信息**：包含作品标题、详细描述、分类标签
- **权限管理**：智能请求相机和相册权限，友好的权限说明
- **预览播放**：发布前可预览视频效果，支持播放控制
- **表单验证**：完整的必填字段验证和错误提示
- **发布状态**：实时显示发布进度和结果反馈

### 👤 个人中心
- **用户资料**：个人信息展示和编辑功能
- **作品管理**：查看和管理自己发布的所有作品
- **金币系统**：查看当前金币余额和消费记录
- **设置选项**：包含屏蔽管理、意见反馈、隐私协议等
- **头像系统**：支持自定义头像，在聊天和个人中心同步显示

### 💰 金币与内购系统
- **金币商店**：提供多种金币套餐选择
- **iOS内购**：完整的苹果内购功能集成
- **订单管理**：购买记录和订单状态跟踪
- **安全支付**：通过苹果官方支付系统保障安全
- **即时到账**：购买成功后金币立即到账

### 🛡️ 内容安全
- **屏蔽管理**：用户可屏蔽不感兴趣的内容
- **举报系统**：支持举报不当内容，维护社区环境
- **内容过滤**：自动过滤已屏蔽的内容
- **安全机制**：多重安全措施保护用户体验

## 使用指南

### 🚀 快速开始
1. **启动应用**：欣赏精美的3D Logo启动动画
2. **浏览作品**：在主页滑动浏览木雕视频作品
3. **AI咨询**：点击底部AI图标，与智能助手对话学习
4. **发布作品**：点击中央发布按钮，分享你的创作
5. **个人管理**：在个人中心查看作品和管理设置

### 📝 发布作品流程
1. **点击发布按钮**：底部导航栏中央的发布图标
2. **填写作品信息**：
   - 输入作品标题（必填）
   - 添加详细描述（必填）
   - 选择作品分类（必填）
3. **上传视频**：
   - 点击视频区域选择上传方式
   - 支持相机录制或相册选择
   - 视频时长限制3分钟内
4. **预览确认**：检查视频播放效果
5. **发布作品**：点击发布按钮完成上传

### 💬 AI助手使用
1. **开始对话**：点击底部AI图标进入聊天界面
2. **提问咨询**：输入你的木雕相关问题
3. **获取指导**：AI助手提供专业的技法建议
4. **查看历史**：所有对话自动保存，可随时回顾
5. **清空记录**：点击右上角清空按钮重新开始

### 🛒 金币购买流程
1. **查看余额**：在个人中心查看当前金币数量
2. **进入商店**：点击金币卡片进入金币商店
3. **选择套餐**：根据需要选择合适的金币套餐
4. **安全支付**：通过苹果官方支付系统完成购买
5. **即时到账**：购买成功后金币立即添加到账户

### 🔧 个人设置
1. **编辑资料**：点击个人中心右上角编辑图标
2. **屏蔽管理**：在设置中管理屏蔽的内容
3. **意见反馈**：通过文字、图片或语音提交反馈
4. **隐私设置**：查看用户协议和隐私政策

## 技术特色

### 🎨 奢华iOS设计系统
- **毛玻璃效果**：导航栏和卡片使用高级毛玻璃背景
- **多层阴影**：精心设计的阴影层次营造立体感
- **闪烁反馈**：按钮点击时的闪烁动画反馈
- **触觉反馈**：完整的iOS触觉反馈体验
- **流畅动画**：60fps的流畅动画和过渡效果

### 🤖 AI技术集成
- **Moonshot AI**：集成专业的AI对话模型
- **专业知识库**：内置30年木雕大师经验
- **智能回复**：支持Markdown格式的富文本回复
- **离线支持**：网络异常时提供基础知识库

### 📱 视频处理技术
- **自动缩略图**：智能生成视频缩略图并缓存
- **多格式支持**：支持多种视频格式上传
- **压缩优化**：自动优化视频大小和质量
- **流畅播放**：优化的视频播放体验

### 💾 数据管理
- **本地存储**：用户数据和设置本地持久化
- **状态同步**：点赞状态在多页面实时同步
- **缓存机制**：智能缓存提升应用性能
- **数据安全**：多重加密保护用户隐私

## 技术架构

### 开发框架
- **Flutter 3.x**：跨平台移动应用开发框架
- **iOS优先**：针对iOS平台深度优化
- **原生性能**：60fps流畅动画和交互

### 项目结构
- **数据层**: TimberLibrary - 数据模型和业务逻辑
- **视图层**: CarveStudio - 用户界面和页面
- **服务层**: WhittleLab - AI服务和数据管理
- **组件层**: WoodChips - 可复用UI组件
- **配置层**: GrainBlue - 主题和样式配置

### 核心技术栈
- **状态管理**: Provider + StreamController
- **网络请求**: HTTP + JSON序列化
- **本地存储**: SharedPreferences
- **视频处理**: video_player + video_thumbnail
- **图像处理**: image_picker + cached_network_image
- **AI集成**: Moonshot API
- **内购系统**: in_app_purchase
- **权限管理**: permission_handler

## 项目完成状态

### ✅ 已完成功能列表

1. **🎬 奢华启动页** - 3D Logo动画 + 发光文字 + 动态背景
2. **📱 主页视频流** - 类抖音视频浏览，木雕作品展示
3. **🎨 高级底部导航栏** - 毛玻璃效果 + 闪烁反馈 + 多层阴影
4. **👤 个人中心** - Sliver滚动 + Tab切换 + 作品网格展示
5. **📝 视频发布系统** - 完整的视频作品创建和发布流程
6. **💰 金币内购系统** - iOS内购功能集成
7. **🤖 AI助手聊天** - Moonshot AI集成，木雕创作指导
8. **💬 意见反馈系统** - 文字、图片、语音反馈
9. **🛡️ 屏蔽管理系统** - 内容过滤和用户安全
10. **🖼️ 视频缩略图系统** - 自动生成和缓存
11. **❤️ 点赞同步系统** - 主页和详情页点赞状态实时同步
12. **🎥 视频管理系统** - 全局视频播放控制和状态管理

### 🎯 项目完成度：100%

所有计划功能均已实现，应用可以直接在iOS设备上运行。

## 特色亮点

- **🤖 AI助手引导**: 为用户提供木雕创作全程指导
- **🎬 奢华启动页**: 3D Logo动画 + 发光文字 + 动态背景
- **🎨 高级底部导航**: 毛玻璃效果 + 闪烁反馈 + 多层阴影
- **👤 个人中心**: Sliver滚动 + Tab切换 + 作品网格展示
- **📱 简洁作品发布**: 专注于作品展示和分享
- **💰 内购系统**: 完整的iOS内购功能集成
- **🎨 Luxury iOS设计**: 遵循高端iOS设计系统规范
- **❤️ 智能同步**: 点赞状态在多页面实时同步
- **🛡️ 内容安全**: 完善的屏蔽和举报机制

---

**开发完成日期**: 2025年7月25日  
**联系邮箱**: <EMAIL>  
**版本**: 1.0.0
