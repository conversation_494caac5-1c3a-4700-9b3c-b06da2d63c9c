import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../GrainBlue/wu_grain_theme.dart';

/// Wuzo用户协议和隐私协议弹窗
class WuTermsAgreementDialog extends StatefulWidget {
  final VoidCallback onAccepted;
  final VoidCallback onRejected;

  const WuTermsAgreementDialog({
    Key? key,
    required this.onAccepted,
    required this.onRejected,
  }) : super(key: key);

  @override
  State<WuTermsAgreementDialog> createState() => _WuTermsAgreementDialogState();

  /// 检查用户是否已同意协议
  static Future<bool> hasUserAcceptedTerms() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('wu_terms_accepted') ?? false;
  }

  /// 保存用户同意协议的状态
  static Future<void> saveUserAcceptance() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('wu_terms_accepted', true);
    await prefs.setString('wu_terms_accepted_date', DateTime.now().toIso8601String());
  }

  /// 显示协议弹窗
  static Future<void> showIfNeeded(BuildContext context, {
    required VoidCallback onAccepted,
    required VoidCallback onRejected,
  }) async {
    final hasAccepted = await hasUserAcceptedTerms();
    if (!hasAccepted && context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => WuTermsAgreementDialog(
          onAccepted: onAccepted,
          onRejected: onRejected,
        ),
      );
    } else {
      onAccepted();
    }
  }
}

class _WuTermsAgreementDialogState extends State<WuTermsAgreementDialog>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOutBack,
    );
    _scaleController.forward();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 用户同意协议
  void _onAccept() async {
    HapticFeedback.lightImpact();
    await WuTermsAgreementDialog.saveUserAcceptance();
    if (mounted) {
      Navigator.of(context).pop();
      widget.onAccepted();
    }
  }

  /// 用户拒绝协议
  void _onReject() {
    HapticFeedback.lightImpact();
    Navigator.of(context).pop();
    widget.onRejected();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async => false, // 禁止返回键关闭
      child: Material(
        color: Colors.black54,
        child: Center(
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              margin: const EdgeInsets.all(20),
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.8,
                maxWidth: MediaQuery.of(context).size.width * 0.9,
              ),
              decoration: BoxDecoration(
                color: WuGrainTheme.canvasWhiteColor,
                borderRadius: BorderRadius.circular(16),
                boxShadow: WuGrainTheme.strongShadow,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildHeader(),
                  Expanded(child: _buildContent()),
                  _buildButtons(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            WuGrainTheme.walnutCarveColor,
            WuGrainTheme.toolSteelColor,
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Icon(
            CupertinoIcons.doc_text,
            color: WuGrainTheme.canvasWhiteColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Terms of Service & Privacy Policy',
              style: TextStyle(
                color: WuGrainTheme.canvasWhiteColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建内容区域
  Widget _buildContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Scrollbar(
        controller: _scrollController,
        thumbVisibility: true,
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTermsOfService(),
              const SizedBox(height: 40),
              _buildPrivacyPolicy(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建用户协议内容
  Widget _buildTermsOfService() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Wuzo Terms of Service (EULA)'),
        _buildSubtitle('Last Updated: July 25, 2025'),
        const SizedBox(height: 16),
        _buildParagraph(
          'By downloading, installing, or using the Wuzo application ("App"), you agree to be bound by these Terms of Service ("Terms"). If you do not agree to these Terms, do not use the App.'
        ),
        _buildSectionHeader('1. Acceptance of Terms'),
        _buildParagraph(
          'By accessing and using Wuzo, you acknowledge that you have read, understood, and agree to be bound by these Terms and our Privacy Policy. These Terms constitute a legally binding agreement between you and Wuzo.'
        ),
        _buildSectionHeader('2. App Description and Services'),
        _buildParagraph('Wuzo is a woodcarving community platform that provides:'),
        _buildBulletPoint('Video sharing and browsing of woodcarving artworks'),
        _buildBulletPoint('AI-powered woodcarving guidance and tutorials'),
        _buildBulletPoint('User-generated content publishing capabilities'),
        _buildBulletPoint('Community interaction features including likes and content management'),
        _buildSectionHeader('3. User Conduct and Content Standards'),
        _buildBoldText('You agree NOT to use the App to:'),
        _buildBulletPoint('Post, upload, or share any content that is illegal, harmful, threatening, abusive, harassing, defamatory, vulgar, obscene, or otherwise objectionable'),
        _buildBulletPoint('Engage in any form of harassment, bullying, or intimidation of other users'),
        _buildBulletPoint('Share content that infringes on intellectual property rights of others'),
        _buildBulletPoint('Distribute spam, malware, or any malicious content'),
        _buildBulletPoint('Impersonate any person or entity or misrepresent your affiliation'),
        const SizedBox(height: 8),
        _buildBoldText('We have zero tolerance for inappropriate content and user misconduct.'),
        _buildSectionHeader('4. Content Moderation and Reporting'),
        _buildBulletPoint('Users can report inappropriate content through the in-app reporting system'),
        _buildBulletPoint('All reported content will be reviewed and processed within 24 hours'),
        _buildBulletPoint('Content that violates these Terms will be removed immediately'),
        _buildBulletPoint('Users who repeatedly violate these Terms may face account suspension or permanent ban'),
        _buildBulletPoint('We reserve the right to remove any content at our sole discretion'),
        _buildSectionHeader('5. Account Suspension and Termination'),
        _buildParagraph('We reserve the right to:'),
        _buildBulletPoint('Suspend or terminate your account immediately for violations of these Terms'),
        _buildBulletPoint('Remove any content that violates our community standards'),
        _buildBulletPoint('Take appropriate legal action for serious violations'),
        _buildBulletPoint('Refuse service to anyone at our sole discretion'),
        _buildSectionHeader('6. Intellectual Property Rights'),
        _buildBulletPoint('You retain ownership of content you create and upload to the App'),
        _buildBulletPoint('By uploading content, you grant Wuzo a non-exclusive license to display and distribute your content within the App'),
        _buildBulletPoint('You represent that you have all necessary rights to the content you upload'),
        _buildBulletPoint('Wuzo respects intellectual property rights and will respond to valid DMCA notices'),
        _buildSectionHeader('7. Limitation of Liability'),
        _buildParagraph('THE APP IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND. WUZO SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES ARISING FROM YOUR USE OF THE APP.'),
        _buildSectionHeader('8. Contact Information'),
        _buildParagraph('For questions, concerns, or to report violations of these Terms:'),
        _buildBoldText('Email: <EMAIL>'),
        _buildSectionHeader('9. Changes to Terms'),
        _buildParagraph('We reserve the right to modify these Terms at any time. Continued use of the App after changes constitutes acceptance of the new Terms. Users will be notified of significant changes through the App.'),
        const SizedBox(height: 16),
        _buildBoldText('By using Wuzo, you acknowledge that you have read and agree to these Terms of Service.'),
      ],
    );
  }

  /// 构建隐私协议内容
  Widget _buildPrivacyPolicy() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('Wuzo Privacy Policy'),
        _buildSubtitle('Last Updated: July 25, 2025'),
        const SizedBox(height: 16),
        _buildParagraph(
          'This Privacy Policy describes how Wuzo ("we," "our," or "us") collects, uses, and protects your information when you use our mobile application.'
        ),
        _buildSectionHeader('1. Information We Collect'),
        _buildBoldText('Personal Information:'),
        _buildBulletPoint('User nickname and profile information you provide'),
        _buildBulletPoint('Custom avatar images you upload'),
        _buildBulletPoint('Content you create and share (videos, descriptions)'),
        const SizedBox(height: 8),
        _buildBoldText('Usage Information:'),
        _buildBulletPoint('App usage patterns and feature interactions'),
        _buildBulletPoint('AI chat conversations for service improvement'),
        _buildBulletPoint('Device information and app performance data'),
        const SizedBox(height: 8),
        _buildBoldText('Local Storage:'),
        _buildBulletPoint('User preferences and settings'),
        _buildBulletPoint('Chat history with AI assistant'),
        _buildSectionHeader('2. How We Use Your Information'),
        _buildParagraph('We use collected information to:'),
        _buildBulletPoint('Provide and improve our woodcarving community services'),
        _buildBulletPoint('Enable AI-powered woodcarving guidance and tutorials'),
        _buildBulletPoint('Maintain app functionality and user preferences'),
        _buildBulletPoint('Respond to user feedback and support requests'),
        _buildBulletPoint('Ensure community safety and content moderation'),
        _buildSectionHeader('3. Information Sharing and Disclosure'),
        _buildBoldText('We do NOT sell, trade, or rent your personal information to third parties.'),
        const SizedBox(height: 8),
        _buildParagraph('We may share information only in these limited circumstances:'),
        _buildBulletPoint('With AI service providers (Moonshot AI) to deliver chat functionality'),
        _buildBulletPoint('To prevent fraud or security threats'),
        _buildSectionHeader('4. Data Storage and Security'),
        _buildBulletPoint('Most user data is stored locally on your device'),
        _buildBulletPoint('Chat conversations are stored locally with optional cloud backup'),
        _buildBulletPoint('We implement industry-standard security measures to protect your data'),
        _buildSectionHeader('5. Third-Party Services'),
        _buildParagraph('Our app integrates with:'),
        _buildBulletPoint('Moonshot AI: For AI chat functionality (subject to their privacy policy)'),
        _buildSectionHeader('6. Children\'s Privacy'),
        _buildParagraph('Our app is not intended for children under 13. We do not knowingly collect personal information from children under 13. If we become aware of such collection, we will delete the information immediately.'),
        _buildSectionHeader('7. Your Privacy Rights'),
        _buildParagraph('You have the right to:'),
        _buildBulletPoint('Access and update your profile information'),
        _buildBulletPoint('Delete your uploaded content'),
        _buildBulletPoint('Clear your AI chat history'),
        _buildBulletPoint('Request deletion of your account data'),
        _buildSectionHeader('8. Data Retention'),
        _buildBulletPoint('Profile information: Retained until account deletion'),
        _buildBulletPoint('Chat history: Stored locally, can be cleared by user'),
        _buildBulletPoint('Usage analytics: Retained for service improvement purposes'),
        _buildSectionHeader('9. Contact Us'),
        _buildParagraph('For privacy-related questions or concerns:'),
        _buildBoldText('Email: <EMAIL>'),
        const SizedBox(height: 8),
        _buildParagraph('We will respond to privacy inquiries within 48 hours.'),
        const SizedBox(height: 16),
        _buildBoldText('By using Wuzo, you consent to the collection and use of information as described in this Privacy Policy.'),
      ],
    );
  }

  /// 构建按钮区域
  Widget _buildButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: WuGrainTheme.pineWoodColor,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _buildButton(
              text: 'Decline',
              onTap: _onReject,
              isAccept: false,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildButton(
              text: 'Accept',
              onTap: _onAccept,
              isAccept: true,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建按钮
  Widget _buildButton({
    required String text,
    required VoidCallback onTap,
    required bool isAccept,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 14),
        decoration: BoxDecoration(
          gradient: isAccept
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    WuGrainTheme.walnutCarveColor,
                    WuGrainTheme.toolSteelColor,
                  ],
                )
              : null,
          color: isAccept ? null : WuGrainTheme.charcoalGrayColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: isAccept
              ? null
              : Border.all(
                  color: WuGrainTheme.charcoalGrayColor.withOpacity(0.3),
                  width: 1,
                ),
          boxShadow: isAccept ? WuGrainTheme.lightShadow : null,
        ),
        child: Center(
          child: Text(
            text,
            style: TextStyle(
              color: isAccept
                  ? WuGrainTheme.canvasWhiteColor
                  : WuGrainTheme.charcoalGrayColor,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: WuGrainTheme.walnutCarveColor,
      ),
    );
  }

  /// 构建副标题
  Widget _buildSubtitle(String subtitle) {
    return Text(
      subtitle,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: WuGrainTheme.toolSteelColor,
      ),
    );
  }

  /// 构建章节头部
  Widget _buildSectionHeader(String header) {
    return Padding(
      padding: const EdgeInsets.only(top: 16, bottom: 8),
      child: Text(
        header,
        style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: WuGrainTheme.toolSteelColor,
        ),
      ),
    );
  }

  /// 构建段落
  Widget _buildParagraph(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          height: 1.5,
          color: WuGrainTheme.charcoalGrayColor,
        ),
      ),
    );
  }

  /// 构建粗体文字
  Widget _buildBoldText(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.bold,
          height: 1.5,
          color: WuGrainTheme.charcoalGrayColor,
        ),
      ),
    );
  }

  /// 构建项目符号
  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4, left: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: TextStyle(
              fontSize: 14,
              color: WuGrainTheme.walnutCarveColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                height: 1.4,
                color: WuGrainTheme.charcoalGrayColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
