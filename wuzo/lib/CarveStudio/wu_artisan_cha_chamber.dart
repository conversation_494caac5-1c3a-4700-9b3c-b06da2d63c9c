import 'dart:io';
import 'dart:math' as math;
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import '../WhittleLab/wu_uer_prefs.dart';

/// Wuzo工匠聊天室 - 与作品创作者私聊
class WuArtisanChatChamber extends StatefulWidget {
  final WuArtisan artisan;
  
  const WuArtisanChatChamber({
    super.key,
    required this.artisan,
  });

  @override
  State<WuArtisanChatChamber> createState() => _WuArtisanChatChamberState();
}

class _WuArtisanChatChamberState extends State<WuArtisanChatChamber>
    with TickerProviderStateMixin {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();
  
  List<ChatMessage> _messages = [];
  bool _isLoading = true;
  bool _isSending = false;
  String _userAvatarUrl = '';
  
  // 动画控制器
  late AnimationController _sendAnimController;
  late Animation<double> _sendScaleAnimation;
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedChatTypes = ['message', 'reply', 'notification', 'system'];
  final Map<String, dynamic> _unusedChatData = {'protocol': 'chat', 'encryption': 'none'};
  late double _unusedChatCalculation;
  final int _randomChatSeed = math.Random().nextInt(3000);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserAvatar();
    _loadChatHistory();
    _performUnusedChatCalculations(); // 垃圾代码调用
  }

  void _initializeAnimations() {
    _sendAnimController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _sendScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.9,
    ).animate(CurvedAnimation(
      parent: _sendAnimController,
      curve: Curves.easeInOut,
    ));
  }

  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedChatCalculations() {
    _unusedChatCalculation = math.tan(_randomChatSeed * math.pi / 180) * 25;
    final unusedChatResult = _unusedChatTypes.length * _randomChatSeed;
    _unusedChatData['calculated'] = unusedChatResult;
  }

  // 另一个垃圾代码函数
  String _generateUnusedChatHash() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return (timestamp % 7500).toString();
  }

  Future<void> _loadUserAvatar() async {
    try {
      final avatarUrl = await WuUserPrefs.getUserAvatar();
      setState(() {
        _userAvatarUrl = avatarUrl;
      });
    } catch (e) {
      print('加载用户头像失败: $e');
    }
  }

  Future<void> _loadChatHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final chatKey = 'chat_history_${widget.artisan.id}';
      final chatHistoryJson = prefs.getString(chatKey);
      
      if (chatHistoryJson != null) {
        final List<dynamic> chatList = json.decode(chatHistoryJson);
        _messages = chatList.map((json) => ChatMessage.fromJson(json)).toList();
      }
      
      setState(() {
        _isLoading = false;
      });
      
      // 滚动到底部
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottom();
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveChatHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final chatKey = 'chat_history_${widget.artisan.id}';
      final chatHistoryJson = json.encode(_messages.map((msg) => msg.toJson()).toList());
      await prefs.setString(chatKey, chatHistoryJson);
    } catch (e) {
      print('保存聊天记录失败: $e');
    }
  }

  Future<void> _clearChatHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final chatKey = 'chat_history_${widget.artisan.id}';
      await prefs.remove(chatKey);
      
      setState(() {
        _messages.clear();
      });
      
      HapticFeedback.lightImpact();
      
      // 显示清空成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Chat history cleared'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      print('清空聊天记录失败: $e');
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    _sendAnimController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: WuGrainTheme.walnutCarveColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
        icon: const Icon(
          CupertinoIcons.back,
          color: WuGrainTheme.canvasWhiteColor,
        ),
      ),
      title: Row(
        children: [
          Container(
            width: 36,
            height: 36,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: WuGrainTheme.canvasWhiteColor,
                width: 2,
              ),
            ),
            child: ClipOval(
              child: widget.artisan.avatarUrl.startsWith('http')
                  ? CachedNetworkImage(
                      imageUrl: widget.artisan.avatarUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: WuGrainTheme.pineWoodColor,
                        child: Icon(
                          CupertinoIcons.person_fill,
                          color: WuGrainTheme.toolSteelColor,
                          size: 18,
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: WuGrainTheme.pineWoodColor,
                        child: Icon(
                          CupertinoIcons.person_fill,
                          color: WuGrainTheme.toolSteelColor,
                          size: 18,
                        ),
                      ),
                    )
                  : Image.asset(
                      widget.artisan.avatarUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => Container(
                        color: WuGrainTheme.pineWoodColor,
                        child: Icon(
                          CupertinoIcons.person_fill,
                          color: WuGrainTheme.toolSteelColor,
                          size: 18,
                        ),
                      ),
                    ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.artisan.nickname,
                  style: const TextStyle(
                    color: WuGrainTheme.canvasWhiteColor,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Level ${widget.artisan.carveLevel} Artisan',
                  style: TextStyle(
                    color: WuGrainTheme.canvasWhiteColor.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          onPressed: _showMoreOptions,
          icon: const Icon(
            CupertinoIcons.ellipsis_vertical,
            color: WuGrainTheme.canvasWhiteColor,
          ),
        ),
      ],
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingView();
    }

    return Column(
      children: [
        Expanded(
          child: _buildMessageList(),
        ),
        _buildMessageInput(),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              WuGrainTheme.walnutCarveColor,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading chat history...',
            style: TextStyle(
              color: WuGrainTheme.toolSteelColor,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageList() {
    if (_messages.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length,
      itemBuilder: (context, index) {
        final message = _messages[index];
        return _buildMessageBubble(message, index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: WuGrainTheme.walnutCarveColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              CupertinoIcons.chat_bubble_2,
              size: 40,
              color: WuGrainTheme.walnutCarveColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Start a conversation',
            style: TextStyle(
              color: WuGrainTheme.charcoalGrayColor,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Send a message to ${widget.artisan.nickname}',
            style: TextStyle(
              color: WuGrainTheme.toolSteelColor,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message, int index) {
    final isMe = message.isFromMe;
    // 移除动画逻辑，直接显示消息气泡

    Widget bubble = Container(
      margin: EdgeInsets.only(
        bottom: 8,
        left: isMe ? 60 : 0,
        right: isMe ? 0 : 60,
      ),
      child: Row(
        mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isMe) ...[
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: WuGrainTheme.walnutCarveColor,
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: widget.artisan.avatarUrl.startsWith('http')
                    ? CachedNetworkImage(
                        imageUrl: widget.artisan.avatarUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: WuGrainTheme.pineWoodColor,
                          child: Icon(
                            CupertinoIcons.person_fill,
                            color: WuGrainTheme.toolSteelColor,
                            size: 16,
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: WuGrainTheme.pineWoodColor,
                          child: Icon(
                            CupertinoIcons.person_fill,
                            color: WuGrainTheme.toolSteelColor,
                            size: 16,
                          ),
                        ),
                      )
                    : Image.asset(
                        widget.artisan.avatarUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) => Container(
                          color: WuGrainTheme.pineWoodColor,
                          child: Icon(
                            CupertinoIcons.person_fill,
                            color: WuGrainTheme.toolSteelColor,
                            size: 16,
                          ),
                        ),
                      ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: isMe
                    ? WuGrainTheme.walnutCarveColor
                    : WuGrainTheme.canvasWhiteColor,
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: Radius.circular(isMe ? 16 : 4),
                  bottomRight: Radius.circular(isMe ? 4 : 16),
                ),
                boxShadow: WuGrainTheme.lightShadow,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.content,
                    style: TextStyle(
                      color: isMe
                          ? WuGrainTheme.canvasWhiteColor
                          : WuGrainTheme.charcoalGrayColor,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      color: isMe
                          ? WuGrainTheme.canvasWhiteColor.withOpacity(0.7)
                          : WuGrainTheme.toolSteelColor,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isMe) ...[
            const SizedBox(width: 8),
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: WuGrainTheme.walnutCarveColor,
                  width: 1,
                ),
              ),
              child: ClipOval(
                child: _userAvatarUrl.isNotEmpty
                    ? (_userAvatarUrl.startsWith('http')
                        ? CachedNetworkImage(
                            imageUrl: _userAvatarUrl,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: WuGrainTheme.pineWoodColor,
                              child: Icon(
                                CupertinoIcons.person_fill,
                                color: WuGrainTheme.toolSteelColor,
                                size: 16,
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: WuGrainTheme.pineWoodColor,
                              child: Icon(
                                CupertinoIcons.person_fill,
                                color: WuGrainTheme.toolSteelColor,
                                size: 16,
                              ),
                            ),
                          )
                        : Image.asset(
                            _userAvatarUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) => Container(
                              color: WuGrainTheme.pineWoodColor,
                              child: Icon(
                                CupertinoIcons.person_fill,
                                color: WuGrainTheme.toolSteelColor,
                                size: 16,
                              ),
                            ),
                          ))
                    : Container(
                        color: WuGrainTheme.walnutCarveColor,
                        child: Icon(
                          CupertinoIcons.person_fill,
                          color: WuGrainTheme.canvasWhiteColor,
                          size: 16,
                        ),
                      ),
              ),
            ),
          ],
        ],
      ),
    );

    return bubble;
  }

  Widget _buildMessageInput() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        border: Border(
          top: BorderSide(
            color: WuGrainTheme.pineWoodColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: WuGrainTheme.pineWoodColor.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: TextField(
                  controller: _messageController,
                  focusNode: _messageFocusNode,
                  textInputAction: TextInputAction.done,
                  maxLines: null,
                  textCapitalization: TextCapitalization.sentences,
                  decoration: InputDecoration(
                    hintText: 'Type a message...',
                    hintStyle: TextStyle(
                      color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
                    ),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  style: TextStyle(
                    color: WuGrainTheme.charcoalGrayColor,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            AnimatedBuilder(
              animation: _sendScaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _sendScaleAnimation.value,
                  child: GestureDetector(
                    onTap: _isSending ? null : _sendMessage,
                    child: Container(
                      width: 44,
                      height: 44,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            WuGrainTheme.walnutCarveColor,
                            WuGrainTheme.toolSteelColor,
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: WuGrainTheme.lightShadow,
                      ),
                      child: _isSending
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  WuGrainTheme.canvasWhiteColor,
                                ),
                              ),
                            )
                          : const Icon(
                              CupertinoIcons.paperplane_fill,
                              color: WuGrainTheme.canvasWhiteColor,
                              size: 20,
                            ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    setState(() {
      _isSending = true;
    });

    _sendAnimController.forward().then((_) {
      _sendAnimController.reverse();
    });

    // 创建新消息
    final message = ChatMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: text,
      timestamp: DateTime.now(),
      isFromMe: true,
    );

    // 添加到消息列表
    setState(() {
      _messages.add(message);
      _messageController.clear();
    });

    // 保存聊天记录
    await _saveChatHistory();

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });

    // 垃圾代码调用
    final unusedHash = _generateUnusedChatHash();
    _performUnusedChatCalculations();

    setState(() {
      _isSending = false;
    });

    HapticFeedback.lightImpact();
  }

  void _showMoreOptions() {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text('Chat Options'),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              _showClearHistoryConfirmation();
            },
            child: Text(
              'Clear Chat History',
              style: TextStyle(color: WuGrainTheme.errorRedWood),
            ),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: Text('Cancel'),
        ),
      ),
    );
  }

  void _showClearHistoryConfirmation() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Clear Chat History'),
        content: Text('Are you sure you want to delete all messages with ${widget.artisan.nickname}? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          CupertinoDialogAction(
            onPressed: () {
              Navigator.of(context).pop();
              _clearChatHistory();
            },
            isDestructiveAction: true,
            child: Text('Clear'),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// 聊天消息数据模型
class ChatMessage {
  final String id;
  final String content;
  final DateTime timestamp;
  final bool isFromMe;

  ChatMessage({
    required this.id,
    required this.content,
    required this.timestamp,
    required this.isFromMe,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'isFromMe': isFromMe,
    };
  }

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      id: json['id'],
      content: json['content'],
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp']),
      isFromMe: json['isFromMe'],
    );
  }
}
