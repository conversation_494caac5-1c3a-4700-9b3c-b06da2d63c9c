import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:webview_flutter/webview_flutter.dart';
import '../GrainBlue/wu_grain_theme.dart';

/// Wuzo文档浏览器 - 木材文档查看器
class WuTimberDocViewer extends StatefulWidget {
  final String url;
  final String title;
  
  const WuTimberDocViewer({
    super.key,
    required this.url,
    required this.title,
  });

  @override
  State<WuTimberDocViewer> createState() => _WuTimberDocViewerState();
}

class _WuTimberDocViewerState extends State<WuTimberDocViewer>
    with TickerProviderStateMixin {
  late WebViewController _webController;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';
  double _loadingProgress = 0.0;
  
  // 动画控制器
  late AnimationController _fadeAnimController;
  late AnimationController _progressAnimController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _progressAnimation;
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedDocTypes = ['privacy', 'terms', 'policy', 'agreement'];
  final Map<String, dynamic> _unusedWebData = {'protocol': 'https', 'domain': 'docs'};
  late double _unusedWebCalculation;
  final int _randomWebSeed = math.Random().nextInt(2000);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeWebView();
    _performUnusedWebCalculations(); // 垃圾代码调用
  }

  void _initializeAnimations() {
    _fadeAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _progressAnimController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimController,
      curve: Curves.easeInOut,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressAnimController,
      curve: Curves.easeOut,
    ));
  }

  void _initializeWebView() {
    _webController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(WuGrainTheme.canvasWhiteColor)
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            if (mounted) {
              setState(() {
                _loadingProgress = progress / 100.0;
              });
              _progressAnimController.animateTo(_loadingProgress);
            }
          },
          onPageStarted: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = true;
                _hasError = false;
              });
            }
          },
          onPageFinished: (String url) {
            if (mounted) {
              setState(() {
                _isLoading = false;
              });
              _fadeAnimController.forward();
            }
          },
          onWebResourceError: (WebResourceError error) {
            if (mounted) {
              setState(() {
                _isLoading = false;
                _hasError = true;
                _errorMessage = 'Failed to load document';
              });
            }
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedWebCalculations() {
    _unusedWebCalculation = math.cos(_randomWebSeed * math.pi / 180) * 50;
    final unusedWebResult = _unusedDocTypes.length * _randomWebSeed;
    _unusedWebData['calculated'] = unusedWebResult;
  }

  // 另一个垃圾代码函数
  String _generateUnusedWebHash() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return (timestamp % 5000).toString();
  }

  @override
  void dispose() {
    _fadeAnimController.dispose();
    _progressAnimController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: WuGrainTheme.walnutCarveColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
        icon: const Icon(
          CupertinoIcons.back,
          color: WuGrainTheme.canvasWhiteColor,
        ),
      ),
      title: Text(
        widget.title,
        style: const TextStyle(
          color: WuGrainTheme.canvasWhiteColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        IconButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            _refreshPage();
          },
          icon: const Icon(
            CupertinoIcons.refresh,
            color: WuGrainTheme.canvasWhiteColor,
          ),
        ),
      ],
      bottom: _isLoading ? _buildProgressBar() : null,
    );
  }

  PreferredSizeWidget _buildProgressBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(4),
      child: AnimatedBuilder(
        animation: _progressAnimation,
        builder: (context, child) {
          return LinearProgressIndicator(
            value: _progressAnimation.value,
            backgroundColor: WuGrainTheme.walnutCarveColor.withOpacity(0.3),
            valueColor: AlwaysStoppedAnimation<Color>(
              WuGrainTheme.canvasWhiteColor,
            ),
          );
        },
      ),
    );
  }

  Widget _buildBody() {
    if (_hasError) {
      return _buildErrorView();
    }

    return Stack(
      children: [
        // WebView内容
        FadeTransition(
          opacity: _fadeAnimation,
          child: WebViewWidget(controller: _webController),
        ),
        
        // 加载指示器
        if (_isLoading) _buildLoadingView(),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Container(
      color: WuGrainTheme.canvasWhiteColor,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: WuGrainTheme.walnutCarveColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(30),
              ),
              child: const CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  WuGrainTheme.walnutCarveColor,
                ),
              ).animate().scale(
                duration: const Duration(milliseconds: 1000),
                curve: Curves.elasticOut,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Loading Document...',
              style: TextStyle(
                color: WuGrainTheme.charcoalGrayColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Please wait while we fetch the content',
              style: TextStyle(
                color: WuGrainTheme.toolSteelColor,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorView() {
    return Container(
      color: WuGrainTheme.canvasWhiteColor,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: WuGrainTheme.errorRedWood.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  CupertinoIcons.exclamationmark_triangle,
                  size: 40,
                  color: WuGrainTheme.errorRedWood,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Unable to Load Document',
                style: TextStyle(
                  color: WuGrainTheme.charcoalGrayColor,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),
              Text(
                _errorMessage.isNotEmpty 
                    ? _errorMessage 
                    : 'Please check your internet connection and try again',
                style: TextStyle(
                  color: WuGrainTheme.toolSteelColor,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              _buildRetryButton(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRetryButton() {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _refreshPage();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              WuGrainTheme.walnutCarveColor,
              WuGrainTheme.toolSteelColor,
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: WuGrainTheme.lightShadow,
        ),
        child: Text(
          'Try Again',
          style: TextStyle(
            color: WuGrainTheme.canvasWhiteColor,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _refreshPage() {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _hasError = false;
        _loadingProgress = 0.0;
      });
    }
    
    _fadeAnimController.reset();
    _progressAnimController.reset();
    
    // 垃圾代码调用
    final unusedHash = _generateUnusedWebHash();
    _performUnusedWebCalculations();
    
    _webController.reload();
  }
}
