import 'dart:math' as math;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import '../SawdustArchive/wu_mock_data.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../WhittleLab/wu_uer_prefs.dart';
import '../WhittleLab/wu_craft_pst_mager.dart';
import '../WhittleLab/wu_thumbil_genator.dart';
import 'wu_profie_dit_pge.dart';
import 'wu_craft_voe_echo.dart';
import 'wu_timber_doc_viewer.dart';
import 'wu_block_manage_vie27.dart';
import 'wu_timber_stream93.dart';


/// Wuzo个人中心页面 - 工匠创作者主页
class WuArtisanHub47 extends StatefulWidget {
  const WuArtisanHub47({super.key});

  @override
  State<WuArtisanHub47> createState() => _WuArtisanHub47State();
}

class _WuArtisanHub47State extends State<WuArtisanHub47>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  
  late TabController _tabController;
  late ScrollController _scrollController;
  
  String _nickname = 'Timber Artisan';
  String _avatarPath = '';
  bool _isLoading = true;
  
  WuArtisan? _currentUser;
  List<WuCraftPost> _userPosts = [];

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _scrollController = ScrollController();
    
    _loadUserData();
    _loadUserPosts();
    _listenToUserPostChanges();
  }

  void _loadUserData() async {
    // 从本地存储加载用户数据
    _nickname = await WuUserPrefs.getUserNickname();
    _avatarPath = await WuUserPrefs.getUserAvatar();
    

    
    // 模拟当前用户（使用第一个预设用户作为基础）
    _currentUser = WuMockData.mockArtisans.first.copyWith(
      nickname: _nickname,
      avatarUrl: _avatarPath,
    );
    
    setState(() {
      _isLoading = false;
    });
  }

  void _loadUserPosts() async {
    await WuCraftPostManager.instance.init();
    _userPosts = WuCraftPostManager.instance.userPosts;
    if (mounted) {
      setState(() {});
    }
  }

  void _listenToUserPostChanges() {
    WuCraftPostManager.instance.postStream.listen((userPosts) {
      if (mounted) {
        setState(() {
          _userPosts = userPosts;
        });
      }
    });
  }

  void _editProfile() async {
    final result = await Navigator.of(context).push<Map<String, String>>(
      CupertinoPageRoute(
        builder: (context) => WuProfileEditPage(
          currentNickname: _nickname,
          currentAvatar: _avatarPath,
        ),
      ),
    );
    
    if (result != null) {
      // 更新本地数据
      setState(() {
        _nickname = result['nickname']!;
        _avatarPath = result['avatar']!;
        _currentUser = _currentUser?.copyWith(
          nickname: _nickname,
          avatarUrl: _avatarPath,
        );
      });
      
      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Profile updated successfully'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }



  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (_isLoading) {
      return const Scaffold(
        backgroundColor: WuGrainTheme.pineWoodColor,
        body: Center(
          child: CircularProgressIndicator(
            color: WuGrainTheme.walnutCarveColor,
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      body: CustomScrollView(
        slivers: [
          // 自定义应用栏
          _buildSliverAppBar(),
          
          // 用户信息卡片
          SliverToBoxAdapter(
            child: _buildUserInfoCard(),
          ),
          
          // // 统计数据
          // SliverToBoxAdapter(
          //   child: _buildStatsSection(),
          // ),
          
          // Tab切换器
          SliverToBoxAdapter(
            child: _buildTabBar(),
          ),
          
          // Tab内容
          SliverToBoxAdapter(
            child: SizedBox(
              height: MediaQuery.of(context).size.height * 0.6,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildSettingsTab(),
                  _buildWorksTab(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Sliver应用栏
  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 60,
      floating: false,
      pinned: true,
      backgroundColor: WuGrainTheme.walnutCarveColor,
      actions: [
        // 编辑资料图标按钮
        Container(
          margin: const EdgeInsets.only(right: 16),
          child: IconButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _editProfile();
            },
            icon: const Icon(
              CupertinoIcons.pencil_circle,
              color: WuGrainTheme.canvasWhiteColor,
              size: 24,
            ),
            style: IconButton.styleFrom(
              // backgroundColor: WuGrainTheme.walnutCarveColor.withOpacity(0.3),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          'Artisan Hub',
          style: TextStyle(
            color: WuGrainTheme.canvasWhiteColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                WuGrainTheme.walnutCarveColor,
                WuGrainTheme.toolSteelColor,
              ],
            ),
          ),
          child: Stack(
            children: [
              // 装饰性木纹
              Positioned.fill(
                child: CustomPaint(
                  painter: WoodGrainDecoration(),
                ),
              ),
            ],
          ),
        ),
      ),
      // actions: [
      //   IconButton(
      //     onPressed: () {
      //       // 设置按钮
      //     },
      //     icon: Icon(
      //       CupertinoIcons.gear_alt,
      //       color: WuGrainTheme.canvasWhiteColor,
      //     ),
      //   ),
      //   IconButton(
      //     onPressed: () {
      //       // 消息按钮
      //     },
      //     icon: Icon(
      //       CupertinoIcons.chat_bubble,
      //       color: WuGrainTheme.canvasWhiteColor,
      //     ),
      //   ),
      // ],
    );
  }

  /// 构建用户信息卡片
  Widget _buildUserInfoCard() {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: WuGrainTheme.strongShadow,
      ),
      child: Column(
        children: [
          // 头像和基本信息
          Row(
            children: [
              // 用户头像
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: WuGrainTheme.walnutCarveColor,
                    width: 3,
                  ),
                  boxShadow: WuGrainTheme.mediumShadow,
                ),
                child: CircleAvatar(
                  radius: 40,
                  backgroundImage: AssetImage(_avatarPath),
                  backgroundColor: WuGrainTheme.pineWoodColor,
                ),
              ),
              
              const SizedBox(width: 20),
              
              // 用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _nickname,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: WuGrainTheme.charcoalGrayColor,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (_currentUser?.isVerified ?? false) ...[
                          const SizedBox(width: 8),
                          Icon(
                            CupertinoIcons.checkmark_seal_fill,
                            size: 20,
                            color: WuGrainTheme.walnutCarveColor,
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Level ${_currentUser?.carveLevel ?? 1} Artisan',
                      style: const TextStyle(
                        fontSize: 14,
                        color: WuGrainTheme.toolSteelColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    // const SizedBox(height: 8),
                    // Text(
                    //   _currentUser?.bio ?? '',
                    //   style: const TextStyle(
                    //     fontSize: 12,
                    //     color: WuGrainTheme.toolSteelColor,
                    //   ),
                    //   maxLines: 2,
                    //   overflow: TextOverflow.ellipsis,
                    // ),
                  ],
                ),
              ),
            ],
          ),
          
           const SizedBox(height: 20),
          
          // 专业技能标签
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _currentUser?.specialties.map((specialty) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: WuGrainTheme.pineWoodColor,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: WuGrainTheme.walnutCarveColor.withOpacity(0.3),
                  ),
                ),
                child: Text(
                  specialty,
                  style: const TextStyle(
                    fontSize: 12,
                    color: WuGrainTheme.toolSteelColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList() ?? [],
          ),
          
          const SizedBox(height: 20),


        ],
      ),
    );
  }

  /// 构建统计数据部分
  Widget _buildStatsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              'Works',
              _userPosts.length.toString(),
              CupertinoIcons.hammer,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Followers',
              _formatCount(_currentUser?.followerCount ?? 0),
              CupertinoIcons.person_2,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildStatCard(
              'Following',
              _formatCount(_currentUser?.followingCount ?? 0),
              CupertinoIcons.person_add,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个统计卡片
  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: WuGrainTheme.lightShadow,
      ),
      child: Column(
        children: [
          Icon(
            icon,
            size: 24,
            color: WuGrainTheme.walnutCarveColor,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: WuGrainTheme.charcoalGrayColor,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: WuGrainTheme.toolSteelColor,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Tab栏
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.fromLTRB(20, 0, 20, 20),
      height: 50, // 降低高度
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: WuGrainTheme.lightShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: TabBar(
          controller: _tabController,
          indicator: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                WuGrainTheme.walnutCarveColor,
                WuGrainTheme.toolSteelColor,
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: WuGrainTheme.walnutCarveColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          indicatorSize: TabBarIndicatorSize.tab, // 让指示器覆盖整个Tab
          indicatorPadding: EdgeInsets.zero, // 移除内边距，让背景色完全覆盖
          labelColor: WuGrainTheme.canvasWhiteColor,
          unselectedLabelColor: WuGrainTheme.toolSteelColor,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          unselectedLabelStyle: const TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 13,
          ),
          dividerColor: Colors.transparent, // 移除分割线
          tabs: const [
            Tab(
              icon: Icon(CupertinoIcons.settings, size: 18),
              text: 'Settings',
            ),
            Tab(
              icon: Icon(CupertinoIcons.square_grid_2x2, size: 18),
              text: 'Works',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建作品Tab
  Widget _buildWorksTab() {
    if (_userPosts.isEmpty) {
      return _buildEmptyWorksState();
    }
    
    return Padding(
      padding: const EdgeInsets.all(10),
      child: GridView.builder(
        padding: EdgeInsets.zero,//去除顶部边距
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 0.8,
        ),
        itemCount: _userPosts.length,
        itemBuilder: (context, index) {
          final post = _userPosts[index];
          return _buildWorkCard(post);
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyWorksState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  WuGrainTheme.walnutCarveColor.withOpacity(0.3),
                  WuGrainTheme.toolSteelColor.withOpacity(0.3),
                ],
              ),
            ),
            child: Icon(
              CupertinoIcons.hammer_fill,
              size: 60,
              color: WuGrainTheme.walnutCarveColor,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Craft Works',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: WuGrainTheme.charcoalGrayColor,
            ),
          ),
          // const SizedBox(height: 8),
          // Text(
          //   'Share your first woodworking creation\nto showcase your craftsmanship!',
          //   textAlign: TextAlign.center,
          //   style: TextStyle(
          //     fontSize: 14,
          //     color: WuGrainTheme.toolSteelColor,
          //     height: 1.5,
          //   ),
          // ),
        ],
      ),
    );
  }

  /// 构建作品卡片
  Widget _buildWorkCard(WuCraftPost post) {
    return GestureDetector(
      onTap: () => _navigateToWorkDetail(post),
      child: Container(
        decoration: BoxDecoration(
          color: WuGrainTheme.canvasWhiteColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: WuGrainTheme.mediumShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 作品缩略图
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  color: WuGrainTheme.charcoalGrayColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: Stack(
                    children: [
                      // 缩略图或占位图
                      _buildThumbnailWidget(post),
                      
                      // 播放图标覆盖层
                      Center(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            CupertinoIcons.play_fill,
                            size: 24,
                            color: WuGrainTheme.canvasWhiteColor,
                          ),
                        ),
                      ),
                      
                      // 播放时长
                      if (post.videoDuration != null)
                        Positioned(
                          bottom: 8,
                          right: 8,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.7),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              _formatDuration(post.videoDuration!),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            
            // 作品信息
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          post.title,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: WuGrainTheme.charcoalGrayColor,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          post.content,
                          style: TextStyle(
                            fontSize: 12,
                            color: WuGrainTheme.toolSteelColor,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                    
                    // 统计信息
                    Row(
                      children: [
                        Icon(
                          CupertinoIcons.heart,
                          size: 14,
                          color: WuGrainTheme.toolSteelColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${post.likeCount}',
                          style: TextStyle(
                            fontSize: 12,
                            color: WuGrainTheme.toolSteelColor,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          CupertinoIcons.eye,
                          size: 14,
                          color: WuGrainTheme.toolSteelColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${post.viewCount}',
                          style: TextStyle(
                            fontSize: 12,
                            color: WuGrainTheme.toolSteelColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建缩略图部件
  Widget _buildThumbnailWidget(WuCraftPost post) {
    return FutureBuilder<String?>(
      future: _getThumbnailPath(post),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: double.infinity,
            height: double.infinity,
            color: WuGrainTheme.charcoalGrayColor,
            child: Center(
              child: CircularProgressIndicator(
                color: WuGrainTheme.canvasWhiteColor,
                strokeWidth: 2,
              ),
            ),
          );
        }
        
        if (snapshot.hasData && snapshot.data != null) {
          final thumbnailPath = snapshot.data!;
          
          // 检查是否是占位符文件（模拟器或生成失败）
          if (thumbnailPath.endsWith('_placeholder.txt') || 
              thumbnailPath.endsWith('_simulator.txt')) {
            return _buildDefaultThumbnail();
          }
          
          return Image.file(
            File(thumbnailPath),
            width: double.infinity,
            height: double.infinity,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              print('缩略图加载失败: $thumbnailPath');
              return _buildDefaultThumbnail();
            },
          );
        }
        
        return _buildDefaultThumbnail();
      },
    );
  }

  /// 获取缩略图路径，如果不存在则生成
  Future<String?> _getThumbnailPath(WuCraftPost post) async {
    if (post.thumbnailPath != null && post.thumbnailPath!.isNotEmpty) {
      final file = File(post.thumbnailPath!);
      if (await file.exists()) {
        return post.thumbnailPath;
      }
    }
    
    // 如果缩略图不存在，尝试生成
    if (post.videoPath != null) {
      final thumbnailPath = await WuThumbnailGenerator.instance.generateThumbnail(post.videoPath!);
      
      // 更新帖子的缩略图路径
      if (thumbnailPath != null) {
        final updatedPost = post.copyWith(thumbnailPath: thumbnailPath);
        await WuCraftPostManager.instance.updatePost(updatedPost);
      }
      
      return thumbnailPath;
    }
    
    return null;
  }

  /// 构建默认缩略图
  Widget _buildDefaultThumbnail() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: WuGrainTheme.charcoalGrayColor,
      child: Icon(
        CupertinoIcons.videocam,
        size: 40,
        color: WuGrainTheme.canvasWhiteColor.withOpacity(0.6),
      ),
    );
  }

  /// 导航到作品详情页
  void _navigateToWorkDetail(WuCraftPost post) async {
    final result = await Navigator.of(context).push<bool>(
      CupertinoPageRoute(
        builder: (context) => WuVideoDetailPage(
          post: post.toCarveBlueprint(),
          isUserPost: true,
        ),
      ),
    );
    
    // 如果删除了作品，刷新列表
    if (result == true) {
      _loadUserPosts();
    }
  }

  /// 构建设置Tab
  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [
          // _buildSettingsItem(
          //   CupertinoIcons.person_circle,
          //   'Account Settings',
          //   'Manage your account information',
          //   () {},
          // ),
          // _buildSettingsItem(
          //   CupertinoIcons.bell,
          //   'Notifications',
          //   'Customize notification preferences',
          //   () {},
          // ),
          // _buildSettingsItem(
          //   CupertinoIcons.lock,
          //   'Privacy & Security',
          //   'Control your privacy settings',
          //   () {},
          // ),
          // _buildSettingsItem(
          //   CupertinoIcons.creditcard,
          //   'Purchase History',
          //   'View your coin purchase orders',
          //   () {
          //     _showOrderHistory();
          //   },
          // ),
          // 新增屏蔽管理入口
          _buildSettingsItem(
            CupertinoIcons.eye_slash,
            'Blocked Content',
            'Manage blocked posts and users',
            () async {
              final result = await Navigator.of(context).push<bool>(
                CupertinoPageRoute(
                  builder: (context) => const WuBlockManageVie27(),
                ),
              );
              
              // 如果屏蔽管理页面返回true，说明有变化，需要通知主页刷新
              if (result == true) {
                // 这里可以通过回调或状态管理通知主页刷新
                // 由于我们已经在主页监听了blockStream，所以会自动刷新
              }
            },
          ),
          _buildSettingsItem(
            CupertinoIcons.doc_text,
            'Privacy Agreement',
            'View our privacy policy and terms',
            () {
              Navigator.of(context).push(
                CupertinoPageRoute(
                  builder: (context) => const WuTimberDocViewer(
                    url: 'https://docs.google.com/document/d/14ZJPGQWU7BBn8j0aKMTGHCd1yuB5NIfpC6CMZplEXN8/edit?usp=sharing',
                    title: 'Privacy Agreement',
                  ),
                ),
              );
            },
          ),
          // _buildSettingsItem(
          //   CupertinoIcons.question_circle,
          //   'Help & Support',
          //   'Get help and contact support',
          //   () {},
          // ),
          _buildSettingsItem(
            CupertinoIcons.chat_bubble_text,
            'Share Your thoughts',
            'Tell us your thoughts and suggestions',
            () {
              Navigator.of(context).push(
                CupertinoPageRoute(
                  builder: (context) => const WuCraftVoiceEcho(),
                ),
              );
            },
          ),
          // _buildSettingsItem(
          //   CupertinoIcons.info_circle,
          //   'About Wuzo',
          //   'App version and information',
          //   () {},
          // ),
          // _buildSettingsItem(
          //   CupertinoIcons.money_dollar_circle,
          //   'Test Add Coins',
          //   'Add 100 test coins (Development only)',
          //   () async {
          //     await WuCoinManager.instance.addCoins(100);
          //     ScaffoldMessenger.of(context).showSnackBar(
          //       SnackBar(
          //         content: const Text('Added 100 test coins'),
          //         backgroundColor: WuGrainTheme.successWoodGreen,
          //       ),
          //     );
          //   },
          // ),
          // const SizedBox(height: 20),
          // _buildSettingsItem(
          //   CupertinoIcons.square_arrow_right,
          //   'Sign Out',
          //   'Sign out from your account',
          //   () {},
          //   isDestructive: true,
          // ),          // const SizedBox(height: 20),
          // _buildSettingsItem(
          //   CupertinoIcons.square_arrow_right,
          //   'Sign Out',
          //   'Sign out from your account',
          //   () {},
          //   isDestructive: true,
          // ),
          // 添加底部安全区域
          //const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 构建设置项
  Widget _buildSettingsItem(
    IconData icon,
    String title,
    String subtitle,
    VoidCallback onTap, {
    bool isDestructive = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: WuGrainTheme.lightShadow,
      ),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: isDestructive 
                ? WuGrainTheme.errorRedWood.withOpacity(0.1)
                : WuGrainTheme.pineWoodColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: isDestructive 
                ? WuGrainTheme.errorRedWood
                : WuGrainTheme.walnutCarveColor,
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: isDestructive 
                ? WuGrainTheme.errorRedWood
                : WuGrainTheme.charcoalGrayColor,
          ),
        ),
        subtitle: Text(
          subtitle,
          style: const TextStyle(
            fontSize: 12,
            color: WuGrainTheme.toolSteelColor,
          ),
        ),
        trailing: Icon(
          CupertinoIcons.chevron_right,
          size: 16,
          color: WuGrainTheme.toolSteelColor,
        ),
        onTap: () {
          HapticFeedback.lightImpact();
          onTap();
        },
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds % 60);
    return '$minutes:$seconds';
  }
}

/// 木纹装饰画笔
class WoodGrainDecoration extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    // 绘制装饰性木纹线条
    for (int i = 0; i < 6; i++) {
      final path = Path();
      final startY = size.height * 0.15 * i;
      final opacity = 0.1;
      
      paint.color = WuGrainTheme.canvasWhiteColor.withOpacity(opacity);
      
      path.moveTo(0, startY);
      
      for (double x = 0; x <= size.width; x += 15) {
        final y = startY + (x / size.width) * 30;
        path.lineTo(x, y);
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
} 