import 'dart:math' as math;
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import '../SawdustArchive/wu_mock_data.dart';
import '../WhittleLab/wu_blok_manger.dart';
import '../WhittleLab/wu_thumbil_genator.dart';
import 'wu_timber_stream93.dart';

/// Wuzo屏蔽管理页面 - 木雕屏蔽内容管理器
class WuBlockManageVie27 extends StatefulWidget {
  const WuBlockManageVie27({super.key});

  @override
  State<WuBlockManageVie27> createState() => _WuBlockManageVie27State();
}

class _WuBlockManageVie27State extends State<WuBlockManageVie27>
    with TickerProviderStateMixin {
  List<CarveBlueprint> _blockedPosts = [];
  bool _isLoading = true;
  bool _isRefreshing = false;
  
  // 动画控制器
  late AnimationController _listAnimController;
  late Animation<double> _listAnimation;
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedBlockCategories = ['content', 'user', 'spam', 'inappropriate'];
  final Map<String, dynamic> _unusedBlockMetrics = {'total': 0, 'today': 0};
  late double _unusedBlockCalculation;
  final int _randomBlockSeed = math.Random().nextInt(2500);
  String _unusedBlockSession = '';
  bool _unusedAnalyticsFlag = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadBlockedPosts();
    _performUnusedBlockCalculations();
    _initializeUnusedBlockMetrics();
  }

  void _initializeAnimations() {
    _listAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _listAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _listAnimController,
      curve: Curves.easeInOutQuart,
    ));
  }

  void _performUnusedBlockCalculations() {
    _unusedBlockCalculation = math.sin(_randomBlockSeed * math.pi / 180) * 100;
    final unusedBlockResult = _unusedBlockCategories.length * _randomBlockSeed;
    _unusedBlockMetrics['calculated'] = unusedBlockResult;
    _unusedBlockSession = DateTime.now().millisecondsSinceEpoch.toString().substring(6);
  }

  void _initializeUnusedBlockMetrics() {
    _unusedAnalyticsFlag = _randomBlockSeed % 3 == 0;
    _unusedBlockMetrics['conversion'] = (_randomBlockSeed % 50) / 50.0;
    _unusedBlockMetrics['engagement'] = (_randomBlockSeed % 75) / 75.0;
  }

  String _generateUnusedBlockToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(8888);
    return '${timestamp % 5000}_${randomValue}_block';
  }

  Future<void> _loadBlockedPosts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await WuBlockManager.instance.init();
      final blockedIds = WuBlockManager.instance.getAllBlockedPostIds();
      final allPosts = WuMockData.mockCarvingPosts;
      _blockedPosts = allPosts.where((post) => blockedIds.contains(post.id)).toList();
      
      setState(() {
        _isLoading = false;
      });
      
      _listAnimController.forward();
      
    } catch (e) {
      print('加载屏蔽列表失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _unblockPost(CarveBlueprint post) async {
    final unusedToken = _generateUnusedBlockToken();
    _performUnusedBlockCalculations();

    try {
      await WuBlockManager.instance.unblockPost(post.id);
      
      setState(() {
        _blockedPosts.removeWhere((p) => p.id == post.id);
      });
      
      HapticFeedback.mediumImpact();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Unblocked "${post.title}"'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
      
    } catch (e) {
      print('解除屏蔽失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Failed to unblock post'),
          backgroundColor: WuGrainTheme.errorRedWood,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  Future<void> _clearAllBlocked() async {
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Clear All Blocked'),
        content: const Text('Are you sure you want to unblock all posts? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Clear All'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await WuBlockManager.instance.clearAllBlocked();
        
        setState(() {
          _blockedPosts.clear();
        });
        
        HapticFeedback.mediumImpact();
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('All blocked posts have been cleared'),
            backgroundColor: WuGrainTheme.successWoodGreen,
            behavior: SnackBarBehavior.floating,
          ),
        );
        
      } catch (e) {
        print('清空屏蔽列表失败: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to clear blocked posts'),
            backgroundColor: WuGrainTheme.errorRedWood,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<void> _refreshBlockedPosts() async {
    setState(() {
      _isRefreshing = true;
    });

    await _loadBlockedPosts();

    setState(() {
      _isRefreshing = false;
    });
  }

  @override
  void dispose() {
    _listAnimController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: WuGrainTheme.walnutCarveColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop(true);
        },
        icon: const Icon(
          CupertinoIcons.back,
          color: WuGrainTheme.canvasWhiteColor,
        ),
      ),
      title: const Text(
        'Blocked Content',
        style: TextStyle(
          color: WuGrainTheme.canvasWhiteColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        if (_blockedPosts.isNotEmpty)
          IconButton(
            onPressed: _clearAllBlocked,
            icon: const Icon(
              CupertinoIcons.clear,
              color: WuGrainTheme.canvasWhiteColor,
            ),
          ),
        // IconButton(
        //   onPressed: _refreshBlockedPosts,
        //   icon: const Icon(
        //     CupertinoIcons.refresh,
        //     color: WuGrainTheme.canvasWhiteColor,
        //   ),
        // ),
      ],
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: WuGrainTheme.luxuryGradient,
              ),
            ),
            child: Center(
              child: SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    WuGrainTheme.canvasWhiteColor,
                  ),
                  strokeWidth: 3,
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Loading Blocked Content...',
            style: TextStyle(
              fontSize: 16,
              color: WuGrainTheme.toolSteelColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_blockedPosts.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _refreshBlockedPosts,
      backgroundColor: WuGrainTheme.canvasWhiteColor,
      color: WuGrainTheme.walnutCarveColor,
      child: AnimatedBuilder(
        animation: _listAnimation,
        builder: (context, child) {
          return Opacity(
            opacity: _listAnimation.value,
            child: Transform.translate(
              offset: Offset(0, 20 * (1 - _listAnimation.value)),
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: _blockedPosts.length,
                itemBuilder: (context, index) {
                  return _buildBlockedPostCard(_blockedPosts[index], index);
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  WuGrainTheme.walnutCarveColor.withOpacity(0.3),
                  WuGrainTheme.toolSteelColor.withOpacity(0.3),
                ],
              ),
            ),
            child: Icon(
              CupertinoIcons.checkmark_shield,
              size: 60,
              color: WuGrainTheme.walnutCarveColor,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No Blocked Content',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: WuGrainTheme.charcoalGrayColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'You haven\'t blocked any posts yet.\nBlocked content will appear here.',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: WuGrainTheme.toolSteelColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBlockedPostCard(CarveBlueprint post, int index) {
    return GestureDetector(
      onTap: () {
        // 跳转到视频详情页
        Navigator.of(context).push(
          CupertinoPageRoute(
            builder: (context) => WuVideoDetailPage(post: post),
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          color: WuGrainTheme.canvasWhiteColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: WuGrainTheme.lightShadow,
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 帖子缩略图
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: WuGrainTheme.charcoalGrayColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: Stack(
                    children: [
                      // 显示视频缩略图
                      Positioned.fill(
                        child: FutureBuilder<String?>(
                          future: Future.value(WuThumbnailGenerator.instance.getThumbnailPath(post.videoUrl)),
                          builder: (context, snapshot) {
                            final thumbnailPath = snapshot.data;
                            
                            if (thumbnailPath != null && File(thumbnailPath).existsSync()) {
                              // 显示生成的缩略图
                              return Image.file(
                                File(thumbnailPath),
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return _buildDefaultThumbnail();
                                },
                              );
                            } else {
                              // 显示默认渐变背景
                              return _buildDefaultThumbnail();
                            }
                          },
                        ),
                      ),
                      // 播放按钮覆盖层
                      Center(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.6),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            CupertinoIcons.play_fill,
                            size: 16,
                            color: WuGrainTheme.canvasWhiteColor,
                          ),
                        ),
                      ),
                      // 屏蔽标识
                      Positioned(
                        top: 4,
                        right: 4,
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: WuGrainTheme.errorRedWood,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            CupertinoIcons.eye_slash_fill,
                            size: 10,
                            color: WuGrainTheme.canvasWhiteColor,
                          ),
                        ),
                      ),
                      // 视频时长
                      Positioned(
                        bottom: 4,
                        right: 4,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            _formatDuration(post.videoDuration),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              // 帖子信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      post.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: WuGrainTheme.charcoalGrayColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'by ${post.creator.nickname}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: WuGrainTheme.toolSteelColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: WuGrainTheme.errorRedWood.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'BLOCKED',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: WuGrainTheme.errorRedWood,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 解除屏蔽按钮
              GestureDetector(
                onTap: () {
                  // 阻止事件冒泡到父级GestureDetector
                  _unblockPost(post);
                },
                child: Column(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: WuGrainTheme.successWoodGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: WuGrainTheme.successWoodGreen.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        CupertinoIcons.checkmark_circle,
                        size: 20,
                        color: WuGrainTheme.successWoodGreen,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Unblock',
                      style: TextStyle(
                        fontSize: 10,
                        color: WuGrainTheme.successWoodGreen,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate(delay: Duration(milliseconds: index * 100))
      .slideX(begin: 0.3, duration: const Duration(milliseconds: 400))
      .fadeIn(duration: const Duration(milliseconds: 400));
  }
  
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds % 60);
    return '$minutes:$seconds';
  }

  Widget _buildDefaultThumbnail() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            WuGrainTheme.walnutCarveColor.withOpacity(0.8),
            WuGrainTheme.toolSteelColor.withOpacity(0.8),
          ],
        ),
      ),
      child: Icon(
        CupertinoIcons.video_camera_solid,
        size: 28,
        color: WuGrainTheme.canvasWhiteColor.withOpacity(0.9),
      ),
    );
  }
} 