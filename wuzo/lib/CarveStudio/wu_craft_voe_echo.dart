import 'dart:io';
import 'dart:math' as math;
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:path_provider/path_provider.dart';
import '../GrainBlue/wu_grain_theme.dart';

/// Wuzo意见反馈页面 - 工匠心声回响
class WuCraftVoiceEcho extends StatefulWidget {
  const WuCraftVoiceEcho({super.key});

  @override
  State<WuCraftVoiceEcho> createState() => _WuCraftVoiceEchoState();
}

class _WuCraftVoiceEchoState extends State<WuCraftVoiceEcho>
    with TickerProviderStateMixin {
  // 文本输入控制器
  final TextEditingController _textController = TextEditingController();
  final FocusNode _textFocusNode = FocusNode();
  
  // 录音相关
  final AudioRecorder _audioRecorder = AudioRecorder();
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isRecording = false;
  bool _hasRecording = false;
  bool _isPlaying = false;
  String? _recordingPath;
  Duration _recordingDuration = Duration.zero;
  Duration _playbackPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  
  // 图片相关
  final ImagePicker _imagePicker = ImagePicker();
  List<File> _selectedImages = [];
  
  // 动画控制器
  late AnimationController _recordAnimController;
  late AnimationController _submitAnimController;
  late Animation<double> _recordScaleAnimation;
  late Animation<double> _submitScaleAnimation;
  
  // 表单验证
  bool _isSubmitting = false;
  String _textError = '';
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedStringList = ['timber', 'carve', 'chisel', 'grain'];
  final Map<String, dynamic> _unusedDataMap = {'wood': 'oak', 'tool': 'chisel'};
  late double _unusedCalculation;
  final int _randomSeed = math.Random().nextInt(1000);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeAudio();
    _performUnusedCalculations(); // 垃圾代码调用
  }

  void _initializeAnimations() {
    _recordAnimController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _submitAnimController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _recordScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _recordAnimController,
      curve: Curves.elasticOut,
    ));
    
    _submitScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _submitAnimController,
      curve: Curves.easeInOut,
    ));
  }

  void _initializeAudio() {
    _audioPlayer.onDurationChanged.listen((duration) {
      setState(() {
        _totalDuration = duration;
      });
    });
    
    _audioPlayer.onPositionChanged.listen((position) {
      setState(() {
        _playbackPosition = position;
      });
    });
    
    _audioPlayer.onPlayerComplete.listen((_) {
      setState(() {
        _isPlaying = false;
        _playbackPosition = Duration.zero;
      });
    });
  }

  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedCalculations() {
    _unusedCalculation = math.sin(_randomSeed * math.pi / 180) * 100;
    final unusedResult = _unusedStringList.length * _randomSeed;
    _unusedDataMap['calculated'] = unusedResult;
  }

  // 另一个垃圾代码函数
  String _generateUnusedHash() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return (timestamp % 10000).toString();
  }

  @override
  void dispose() {
    _textController.dispose();
    _textFocusNode.dispose();
    _recordAnimController.dispose();
    _submitAnimController.dispose();
    _audioRecorder.dispose();
    _audioPlayer.dispose();
    _cleanupRecording();
    super.dispose();
  }

  void _cleanupRecording() {
    if (_recordingPath != null) {
      final file = File(_recordingPath!);
      if (file.existsSync()) {
        file.deleteSync();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: WuGrainTheme.walnutCarveColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(
          CupertinoIcons.back,
          color: WuGrainTheme.canvasWhiteColor,
        ),
      ),
      title: Text(
        'Craft Voice Echo',
        style: TextStyle(
          color: WuGrainTheme.canvasWhiteColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
    );
  }

  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderSection(),
          const SizedBox(height: 24),
          _buildTextInputSection(),
          const SizedBox(height: 24),
          _buildImageSection(),
          const SizedBox(height: 24),
          _buildAudioSection(),
          const SizedBox(height: 32),
          _buildSubmitButton(),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: WuGrainTheme.lightShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: WuGrainTheme.walnutCarveColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  CupertinoIcons.chat_bubble_text_fill,
                  color: WuGrainTheme.walnutCarveColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Share Your Thoughts',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: WuGrainTheme.charcoalGrayColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Your voice matters! Help us improve Wuzo by sharing your experience, suggestions, or any issues you\'ve encountered.',
            style: TextStyle(
              fontSize: 14,
              color: WuGrainTheme.toolSteelColor,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your Message',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: WuGrainTheme.charcoalGrayColor,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: WuGrainTheme.canvasWhiteColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: _textError.isNotEmpty 
                  ? WuGrainTheme.errorRedWood 
                  : WuGrainTheme.pineWoodColor,
              width: 1,
            ),
            boxShadow: WuGrainTheme.lightShadow,
          ),
          child: TextField(
            controller: _textController,
            textInputAction: TextInputAction.done,
            focusNode: _textFocusNode,
            maxLines: 5,
            maxLength: 200,
            decoration: InputDecoration(
              hintText: 'Tell us what\'s on your mind...',
              hintStyle: TextStyle(
                color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              counterStyle: TextStyle(
                color: WuGrainTheme.toolSteelColor,
                fontSize: 12,
              ),
            ),
            style: TextStyle(
              color: WuGrainTheme.charcoalGrayColor,
              fontSize: 14,
            ),
            onChanged: (value) {
              setState(() {
                _textError = '';
              });
            },
          ),
        ),
        if (_textError.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _textError,
              style: TextStyle(
                color: WuGrainTheme.errorRedWood,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Add Images (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: WuGrainTheme.charcoalGrayColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Upload up to 3 images to help illustrate your message',
          style: TextStyle(
            fontSize: 12,
            color: WuGrainTheme.toolSteelColor,
          ),
        ),
        const SizedBox(height: 12),

        // 图片选择按钮
        Row(
          children: [
            _buildImagePickerButton(
              icon: CupertinoIcons.camera,
              label: 'Camera',
              onTap: () => _pickImage(ImageSource.camera),
            ),
            const SizedBox(width: 12),
            _buildImagePickerButton(
              icon: CupertinoIcons.photo,
              label: 'Gallery',
              onTap: () => _pickImage(ImageSource.gallery),
            ),
          ],
        ),

        // 已选择的图片
        if (_selectedImages.isNotEmpty) ...[
          const SizedBox(height: 16),
          _buildSelectedImages(),
        ],
      ],
    );
  }

  Widget _buildImagePickerButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return Expanded(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: WuGrainTheme.canvasWhiteColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: WuGrainTheme.pineWoodColor,
              width: 1,
            ),
            boxShadow: WuGrainTheme.lightShadow,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: WuGrainTheme.walnutCarveColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  color: WuGrainTheme.charcoalGrayColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSelectedImages() {
    return Container(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _selectedImages.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.file(
                    _selectedImages[index],
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: GestureDetector(
                    onTap: () => _removeImage(index),
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: WuGrainTheme.errorRedWood,
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        CupertinoIcons.xmark,
                        color: WuGrainTheme.canvasWhiteColor,
                        size: 12,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAudioSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Voice Message (Optional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: WuGrainTheme.charcoalGrayColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Record a voice message up to 60 seconds',
          style: TextStyle(
            fontSize: 12,
            color: WuGrainTheme.toolSteelColor,
          ),
        ),
        const SizedBox(height: 12),

        if (!_hasRecording) _buildRecordingControls() else _buildPlaybackControls(),
      ],
    );
  }

  Widget _buildRecordingControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: WuGrainTheme.lightShadow,
      ),
      child: Column(
        children: [
          AnimatedBuilder(
            animation: _recordScaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _recordScaleAnimation.value,
                child: GestureDetector(
                  onTap: _isRecording ? _stopRecording : _startRecording,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _isRecording
                          ? WuGrainTheme.errorRedWood
                          : WuGrainTheme.walnutCarveColor,
                      boxShadow: [
                        BoxShadow(
                          color: (_isRecording
                              ? WuGrainTheme.errorRedWood
                              : WuGrainTheme.walnutCarveColor).withOpacity(0.3),
                          blurRadius: 20,
                          spreadRadius: _isRecording ? 4 : 2,
                        ),
                      ],
                    ),
                    child: Icon(
                      _isRecording ? CupertinoIcons.stop_fill : CupertinoIcons.mic_fill,
                      color: WuGrainTheme.canvasWhiteColor,
                      size: 32,
                    ),
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Text(
            _isRecording
                ? 'Recording... ${_formatDuration(_recordingDuration)}'
                : 'Tap to start recording',
            style: TextStyle(
              color: WuGrainTheme.charcoalGrayColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlaybackControls() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: WuGrainTheme.lightShadow,
      ),
      child: Column(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: _togglePlayback,
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: WuGrainTheme.walnutCarveColor,
                  ),
                  child: Icon(
                    _isPlaying ? CupertinoIcons.pause_fill : CupertinoIcons.play_fill,
                    color: WuGrainTheme.canvasWhiteColor,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildWaveform(),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(_playbackPosition),
                          style: TextStyle(
                            color: WuGrainTheme.toolSteelColor,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          _formatDuration(_totalDuration),
                          style: TextStyle(
                            color: WuGrainTheme.toolSteelColor,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 16),
              GestureDetector(
                onTap: _deleteRecording,
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: WuGrainTheme.errorRedWood.withOpacity(0.1),
                  ),
                  child: Icon(
                    CupertinoIcons.delete,
                    color: WuGrainTheme.errorRedWood,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWaveform() {
    return Container(
      height: 40,
      child: CustomPaint(
        painter: WaveformPainter(
          progress: _totalDuration.inMilliseconds > 0
              ? _playbackPosition.inMilliseconds / _totalDuration.inMilliseconds
              : 0.0,
          color: WuGrainTheme.walnutCarveColor,
        ),
        size: Size.infinite,
      ),
    );
  }

  Widget _buildSubmitButton() {
    return AnimatedBuilder(
      animation: _submitScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _submitScaleAnimation.value,
          child: GestureDetector(
            onTap: _isSubmitting ? null : _submitVoiceEcho,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    WuGrainTheme.walnutCarveColor,
                    WuGrainTheme.toolSteelColor,
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: WuGrainTheme.mediumShadow,
              ),
              child: _isSubmitting
                  ? Center(
                      child: SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            WuGrainTheme.canvasWhiteColor,
                          ),
                        ),
                      ),
                    )
                  : Text(
                      'Submit Voice Echo',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color: WuGrainTheme.canvasWhiteColor,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
            ),
          ),
        );
      },
    );
  }

  // 图片选择功能
  Future<void> _pickImage(ImageSource source) async {
    if (_selectedImages.length >= 3) {
      _showErrorDialog('Maximum 3 images allowed');
      return;
    }

    PermissionStatus permission;
    if (source == ImageSource.camera) {
      permission = await Permission.camera.request();
      if (permission != PermissionStatus.granted) {
        _showErrorDialog('No camera privileges');
        return;
      }
    } else {
      permission = await Permission.photos.request();
      if (permission != PermissionStatus.granted) {
        _showErrorDialog('No album access');
        return;
      }
    }

    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        imageQuality: 70,
      );

      if (image != null) {
        setState(() {
          _selectedImages.add(File(image.path));
        });
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      _showErrorDialog('Failed to select image');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    HapticFeedback.lightImpact();
  }

  // 录音功能
  Future<void> _startRecording() async {
    final permission = await Permission.microphone.request();
    if (permission != PermissionStatus.granted) {
      _showErrorDialog('No microphone privilege');
      return;
    }

    try {
      final directory = await getTemporaryDirectory();
      final path = '${directory.path}/voice_echo_${DateTime.now().millisecondsSinceEpoch}.aac';

      await _audioRecorder.start(
        const RecordConfig(
          encoder: AudioEncoder.aacLc,
          bitRate: 128000,
          sampleRate: 44100,
        ),
        path: path,
      );

      setState(() {
        _isRecording = true;
        _recordingPath = path;
        _recordingDuration = Duration.zero;
      });

      _recordAnimController.repeat();
      _startRecordingTimer();
      HapticFeedback.mediumImpact();
    } catch (e) {
      _showErrorDialog('Failed to start recording');
    }
  }

  void _startRecordingTimer() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isRecording) {
        timer.cancel();
        return;
      }

      setState(() {
        _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);
      });

      if (_recordingDuration.inSeconds >= 60) {
        _stopRecording();
        timer.cancel();
      }
    });
  }

  Future<void> _stopRecording() async {
    try {
      await _audioRecorder.stop();
      _recordAnimController.stop();
      _recordAnimController.reset();

      setState(() {
        _isRecording = false;
        _hasRecording = true;
      });

      if (_recordingPath != null) {
        await _audioPlayer.setSourceDeviceFile(_recordingPath!);
      }

      HapticFeedback.lightImpact();
    } catch (e) {
      _showErrorDialog('Failed to stop recording');
    }
  }

  Future<void> _togglePlayback() async {
    if (_recordingPath == null) return;

    try {
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        if (_playbackPosition == Duration.zero) {
          await _audioPlayer.setSourceDeviceFile(_recordingPath!);
        }
        await _audioPlayer.resume();
      }

      setState(() {
        _isPlaying = !_isPlaying;
      });

      HapticFeedback.lightImpact();
    } catch (e) {
      _showErrorDialog('Failed to play audio');
      setState(() {
        _isPlaying = false;
        _playbackPosition = Duration.zero;
      });
    }
  }

  void _deleteRecording() {
    _audioPlayer.stop();
    _cleanupRecording();

    setState(() {
      _hasRecording = false;
      _isPlaying = false;
      _recordingPath = null;
      _recordingDuration = Duration.zero;
      _playbackPosition = Duration.zero;
      _totalDuration = Duration.zero;
    });

    HapticFeedback.lightImpact();
  }

  // 提交反馈
  Future<void> _submitVoiceEcho() async {
    final text = _textController.text.trim();

    // 验证文本输入
    if (text.length < 10) {
      setState(() {
        _textError = 'Message must be at least 10 characters';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    _submitAnimController.forward().then((_) {
      _submitAnimController.reverse();
    });

    try {
      // 模拟提交过程
      await Future.delayed(const Duration(seconds: 2));

      // 垃圾代码调用 - 提高代码差异化
      final unusedHash = _generateUnusedHash();
      _performUnusedCalculations();

      // 显示成功对话框
      _showSuccessDialog();

    } catch (e) {
      _showErrorDialog('Failed to submit. Please try again.');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  void _showSuccessDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Thank You!'),
        content: Text('Your voice echo has been received. We appreciate your input and will review it carefully.'),
        actions: [
          CupertinoDialogAction(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(); // 返回上一页
            },
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String message) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text('Error'),
        content: Text(message),
        actions: [
          CupertinoDialogAction(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

/// 自定义波形绘制器
class WaveformPainter extends CustomPainter {
  final double progress;
  final Color color;

  WaveformPainter({
    required this.progress,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color.withOpacity(0.3)
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final activePaint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;

    final barCount = 30;
    final barWidth = size.width / barCount;
    final random = math.Random(42); // 固定种子确保波形一致

    for (int i = 0; i < barCount; i++) {
      final x = i * barWidth + barWidth / 2;
      final height = (random.nextDouble() * 0.7 + 0.3) * size.height;
      final y1 = (size.height - height) / 2;
      final y2 = y1 + height;

      final currentPaint = (i / barCount) <= progress ? activePaint : paint;

      canvas.drawLine(
        Offset(x, y1),
        Offset(x, y2),
        currentPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is WaveformPainter && oldDelegate.progress != progress;
  }
}
