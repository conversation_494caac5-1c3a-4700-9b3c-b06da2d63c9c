import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import '../WhittleLab/wu_craft_pst_mager.dart';
import '../WhittleLab/wu_uer_prefs.dart';
import '../WhittleLab/wu_thumbil_genator.dart';


/// Wuzo作品发布页面 - 木雕创作发布工坊
class WuCraftPublishVie extends StatefulWidget {
  const WuCraftPublishVie({super.key});

  @override
  State<WuCraftPublishVie> createState() => _WuCraftPublishVieState();
}

class _WuCraftPublishVieState extends State<WuCraftPublishVie>
    with TickerProviderStateMixin {
  
  // 表单控制器
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _contentController = TextEditingController();
  final FocusNode _titleFocusNode = FocusNode();
  final FocusNode _contentFocusNode = FocusNode();
  
  // 视频相关
  final ImagePicker _imagePicker = ImagePicker();
  File? _selectedVideo;
  VideoPlayerController? _videoController;
  bool _isVideoInitialized = false;
  bool _isVideoPlaying = false;
  Duration? _videoDuration;
  
  // 表单状态
  String _selectedCategory = '';
  bool _isPublishing = false;
  String _titleError = '';
  String _contentError = '';
  String _categoryError = '';
  String _videoError = '';


  
  // 动画控制器
  late AnimationController _publishAnimController;
  late AnimationController _videoAnimController;
  late Animation<double> _publishScaleAnimation;
  late Animation<double> _videoScaleAnimation;
  
  // 话题分类选项
  final List<String> _categories = [
    'Traditional Carving',
    'Modern Sculpture',
    'Animal Figures',
    'Decorative Arts',
    'Miniature Works',
    'Relief Carving',
    'Abstract Design',
    'Portrait Carving',
  ];
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedPublishTypes = ['video', 'image', 'tutorial', 'showcase'];
  final Map<String, dynamic> _unusedPublishData = {'format': 'mp4', 'quality': 'high'};
  late double _unusedPublishCalculation;
  final int _randomPublishSeed = math.Random().nextInt(4000);
  String _unusedPublishSession = '';
  bool _unusedValidationFlag = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();

    _performUnusedPublishCalculations(); // 垃圾代码调用
    _initializeUnusedPublishMetrics(); // 垃圾代码调用
  }

  void _initializeAnimations() {
    _publishAnimController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _videoAnimController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _publishScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _publishAnimController,
      curve: Curves.easeInOut,
    ));
    
    _videoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _videoAnimController,
      curve: Curves.elasticOut,
    ));
  }



  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedPublishCalculations() {
    _unusedPublishCalculation = math.atan(_randomPublishSeed * math.pi / 180) * 60;
    final unusedPublishResult = _unusedPublishTypes.length * _randomPublishSeed;
    _unusedPublishData['calculated'] = unusedPublishResult;
    _unusedPublishSession = DateTime.now().millisecondsSinceEpoch.toString().substring(9);
  }

  // 另一个垃圾代码函数
  void _initializeUnusedPublishMetrics() {
    _unusedValidationFlag = _randomPublishSeed % 6 == 0;
    _unusedPublishData['compression'] = (_randomPublishSeed % 60) / 60.0;
    _unusedPublishData['resolution'] = (_randomPublishSeed % 720) + 480;
  }

  // 第三个垃圾代码函数
  String _generateUnusedPublishHash() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(8888);
    return '${timestamp % 9000}_${randomValue}_publish';
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _titleFocusNode.dispose();
    _contentFocusNode.dispose();
    _publishAnimController.dispose();
    _videoAnimController.dispose();
    _videoController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 垃圾代码调用 - 页面构建时的差异化代码
    final unusedBuildToken = _generateUnusedPublishHash();
    _unusedPublishData['buildCount'] = (_unusedPublishData['buildCount'] ?? 0) + 1;

    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: WuGrainTheme.walnutCarveColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.of(context).pop();
        },
        icon: const Icon(
          CupertinoIcons.back,
          color: WuGrainTheme.canvasWhiteColor,
        ),
      ),
      title: const Text(
        'Create Craft',
        style: TextStyle(
          color: WuGrainTheme.canvasWhiteColor,
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      centerTitle: true,
      actions: [
        if (!_isPublishing)
          IconButton(
            onPressed: _clearForm,
            icon: const Icon(
              CupertinoIcons.delete_simple,
              color: WuGrainTheme.canvasWhiteColor,
            ),
          ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题输入区域
          _buildTitleInput(),
          
          const SizedBox(height: 20),
          
          // 内容输入区域
          _buildContentInput(),
          
          const SizedBox(height: 20),
          
          // 视频选择区域
          _buildVideoSelection(),
          
          const SizedBox(height: 20),
          
          // 话题分类选择
          _buildCategorySelection(),
          
          const SizedBox(height: 20),



          // 发布按钮
          _buildPublishButton(),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  /// 构建标题输入框
  Widget _buildTitleInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              CupertinoIcons.textformat,
              size: 20,
              color: WuGrainTheme.toolSteelColor,
            ),
            const SizedBox(width: 8),
            const Text(
              'Title',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.charcoalGrayColor,
              ),
            ),
            const Text(
              ' *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: WuGrainTheme.canvasWhiteColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: WuGrainTheme.lightShadow,
            border: Border.all(
              color: _titleError.isNotEmpty 
                  ? WuGrainTheme.errorRedWood 
                  : WuGrainTheme.pineWoodColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: _titleController,
            focusNode: _titleFocusNode,
            maxLength: 50,
            decoration: InputDecoration(
              hintText: 'Enter craft title...',
              hintStyle: TextStyle(
                color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              counterText: '',
            ),
            style: const TextStyle(
              fontSize: 16,
              color: WuGrainTheme.charcoalGrayColor,
            ),
            onChanged: (_) {
              if (_titleError.isNotEmpty) {
                setState(() {
                  _titleError = '';
                });
              }
            },
          ),
        ),
        if (_titleError.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 4),
            child: Text(
              _titleError,
              style: const TextStyle(
                fontSize: 12,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建内容输入框
  Widget _buildContentInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              CupertinoIcons.doc_text,
              size: 20,
              color: WuGrainTheme.toolSteelColor,
            ),
            const SizedBox(width: 8),
            const Text(
              'Content',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.charcoalGrayColor,
              ),
            ),
            const Text(
              ' *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          height: 120,
          decoration: BoxDecoration(
            color: WuGrainTheme.canvasWhiteColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: WuGrainTheme.lightShadow,
            border: Border.all(
              color: _contentError.isNotEmpty 
                  ? WuGrainTheme.errorRedWood 
                  : WuGrainTheme.pineWoodColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: _contentController,
            textInputAction: TextInputAction.done,
            focusNode: _contentFocusNode,
            maxLength: 500,
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: InputDecoration(
              hintText: 'Describe your craft work, techniques used, materials...',
              hintStyle: TextStyle(
                color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
                fontSize: 14,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              counterText: '',
            ),
            style: const TextStyle(
              fontSize: 14,
              color: WuGrainTheme.charcoalGrayColor,
              height: 1.4,
            ),
            onChanged: (_) {
              if (_contentError.isNotEmpty) {
                setState(() {
                  _contentError = '';
                });
              }
            },
          ),
        ),
        if (_contentError.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 4, left: 4),
            child: Text(
              _contentError,
              style: const TextStyle(
                fontSize: 12,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建视频选择区域
  Widget _buildVideoSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              CupertinoIcons.videocam,
              size: 20,
              color: WuGrainTheme.toolSteelColor,
            ),
            const SizedBox(width: 8),
            const Text(
              'Video',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.charcoalGrayColor,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              '*',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        
        if (_selectedVideo == null)
          _buildVideoUploadArea()
        else
          _buildVideoPreview(),
          
        // 显示视频错误信息
        if (_videoError.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              _videoError,
              style: TextStyle(
                fontSize: 12,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ),
      ],
    );
  }

  /// 构建视频上传区域
  Widget _buildVideoUploadArea() {
    return Container(
      width: double.infinity,
      height: 300,
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: WuGrainTheme.lightShadow,
        border: Border.all(
          color: WuGrainTheme.pineWoodColor.withOpacity(0.3),
          width: 1,
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            CupertinoIcons.cloud_upload,
            size: 48,
            color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
          ),
          const SizedBox(height: 16),
          const Text(
            'Add Video',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: WuGrainTheme.charcoalGrayColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Record or select from gallery',
            style: TextStyle(
              fontSize: 12,
              color: WuGrainTheme.toolSteelColor.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildVideoActionButton(
                icon: CupertinoIcons.camera,
                label: 'Record',
                onTap: () => _selectVideo(ImageSource.camera),
              ),
              const SizedBox(width: 20),
              _buildVideoActionButton(
                icon: CupertinoIcons.photo_on_rectangle,
                label: 'Gallery',
                onTap: () => _selectVideo(ImageSource.gallery),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建视频操作按钮
  Widget _buildVideoActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: WuGrainTheme.walnutCarveColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: WuGrainTheme.walnutCarveColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: WuGrainTheme.walnutCarveColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: WuGrainTheme.walnutCarveColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建视频预览
  Widget _buildVideoPreview() {
    return AnimatedBuilder(
      animation: _videoScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _videoScaleAnimation.value,
          child: Container(
            width: double.infinity,
            height: 300,
            decoration: BoxDecoration(
              color: WuGrainTheme.charcoalGrayColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: WuGrainTheme.mediumShadow,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // 视频播放器 - 使用FittedBox避免变形
                  if (_isVideoInitialized && _videoController != null)
                    Positioned.fill(
                      child: FittedBox(
                        fit: BoxFit.cover,
                        child: SizedBox(
                          width: _videoController!.value.size.width,
                          height: _videoController!.value.size.height,
                          child: VideoPlayer(_videoController!),
                        ),
                      ),
                    )
                  else
                    Positioned.fill(
                      child: Container(
                        color: WuGrainTheme.charcoalGrayColor,
                        child: Icon(
                          CupertinoIcons.play_circle,
                          size: 60,
                          color: WuGrainTheme.canvasWhiteColor.withOpacity(0.8),
                        ),
                      ),
                    ),
                  
                  // 播放控制覆盖层
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.3),
                          ],
                        ),
                      ),
                      child: Center(
                        child: GestureDetector(
                          onTap: _toggleVideoPlayback,
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.6),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _isVideoPlaying 
                                  ? CupertinoIcons.pause_fill 
                                  : CupertinoIcons.play_fill,
                              size: 24,
                              color: WuGrainTheme.canvasWhiteColor,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  
                  // 删除按钮
                  Positioned(
                    top: 8,
                    right: 8,
                    child: GestureDetector(
                      onTap: _removeVideo,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(
                          color: WuGrainTheme.errorRedWood,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          CupertinoIcons.xmark,
                          size: 16,
                          color: WuGrainTheme.canvasWhiteColor,
                        ),
                      ),
                    ),
                  ),
                  
                  // 视频信息
                  if (_videoDuration != null)
                    Positioned(
                      bottom: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.7),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          _formatDuration(_videoDuration!),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建分类选择
  Widget _buildCategorySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              CupertinoIcons.tag,
              size: 20,
              color: WuGrainTheme.toolSteelColor,
            ),
            const SizedBox(width: 8),
            const Text(
              'Category',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.charcoalGrayColor,
              ),
            ),
            const Text(
              ' *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _categories.map((category) {
            final isSelected = _selectedCategory == category;
            return GestureDetector(
              onTap: () {
                setState(() {
                  _selectedCategory = category;
                  _categoryError = '';
                });
                HapticFeedback.lightImpact();
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? WuGrainTheme.walnutCarveColor 
                      : WuGrainTheme.canvasWhiteColor,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: isSelected 
                        ? WuGrainTheme.walnutCarveColor 
                        : WuGrainTheme.pineWoodColor.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: isSelected ? WuGrainTheme.lightShadow : null,
                ),
                child: Text(
                  category,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: isSelected 
                        ? WuGrainTheme.canvasWhiteColor 
                        : WuGrainTheme.charcoalGrayColor,
                  ),
                ),
              ),
            ).animate(delay: Duration(milliseconds: _categories.indexOf(category) * 50))
              .fadeIn(duration: const Duration(milliseconds: 200))
              .scale(begin: const Offset(0.8, 0.8));
          }).toList(),
        ),
        if (_categoryError.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8, left: 4),
            child: Text(
              _categoryError,
              style: const TextStyle(
                fontSize: 12,
                color: WuGrainTheme.errorRedWood,
              ),
            ),
          ),
      ],
    );
  }



  /// 构建发布按钮
  Widget _buildPublishButton() {
    return AnimatedBuilder(
      animation: _publishScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _publishScaleAnimation.value,
          child: GestureDetector(
            onTap: _isPublishing ? null : _publishPost,
            child: Container(
              width: double.infinity,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: _isPublishing
                      ? [
                          WuGrainTheme.toolSteelColor.withOpacity(0.6),
                          WuGrainTheme.charcoalGrayColor.withOpacity(0.6),
                        ]
                      : [
                          WuGrainTheme.walnutCarveColor,
                          WuGrainTheme.toolSteelColor,
                        ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: _isPublishing ? null : WuGrainTheme.mediumShadow,
              ),
              child: Center(
                child: _isPublishing
                    ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                WuGrainTheme.canvasWhiteColor,
                              ),
                              strokeWidth: 2,
                            ),
                          ),
                          const SizedBox(width: 12),
                          const Text(
                            'Publishing...',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: WuGrainTheme.canvasWhiteColor,
                            ),
                          ),
                        ],
                      )
                    : const Text(
                        'Publish Craft',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: WuGrainTheme.canvasWhiteColor,
                        ),
                      ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 选择视频
  Future<void> _selectVideo(ImageSource source) async {
    try {
      // 请求权限
      final Permission permission = source == ImageSource.camera 
          ? Permission.camera 
          : Permission.photos;
      
      final PermissionStatus permissionStatus = await permission.request();
      
      if (permissionStatus.isDenied || permissionStatus.isPermanentlyDenied) {
        _showPermissionDeniedMessage(source);
        return;
      }
      
      // 垃圾代码调用
      final unusedHash = _generateUnusedPublishHash();
      _performUnusedPublishCalculations();
      
      // 选择视频
      final XFile? video = await _imagePicker.pickVideo(
        source: source,
        maxDuration: const Duration(minutes: 3),
      );
      
      if (video != null) {
        final File videoFile = File(video.path);
        
        setState(() {
          _selectedVideo = videoFile;
          _isVideoInitialized = false;
          _isVideoPlaying = false;
          _videoError = ''; // 清空视频错误信息
        });
        
        // 初始化视频播放器
        await _initializeVideoPlayer();
        
        // 播放入场动画
        _videoAnimController.forward();
        
        HapticFeedback.mediumImpact();
      }
      
    } catch (e) {
      print('选择视频失败: $e');
      _showErrorMessage('Failed to select video');
    }
  }

  /// 初始化视频播放器
  Future<void> _initializeVideoPlayer() async {
    if (_selectedVideo == null) return;
    
    try {
      _videoController?.dispose();
      _videoController = VideoPlayerController.file(_selectedVideo!);
      
      await _videoController!.initialize();
      
      setState(() {
        _isVideoInitialized = true;
        _videoDuration = _videoController!.value.duration;
      });
      
    } catch (e) {
      print('视频初始化失败: $e');
      setState(() {
        _isVideoInitialized = false;
      });
    }
  }

  /// 切换视频播放状态
  void _toggleVideoPlayback() {
    if (!_isVideoInitialized || _videoController == null) return;
    
    setState(() {
      if (_isVideoPlaying) {
        _videoController!.pause();
        _isVideoPlaying = false;
      } else {
        _videoController!.play();
        _isVideoPlaying = true;
      }
    });
    
    HapticFeedback.lightImpact();
  }

  /// 移除视频
  void _removeVideo() {
    _videoController?.dispose();
    _videoController = null;
    
    setState(() {
      _selectedVideo = null;
      _isVideoInitialized = false;
      _isVideoPlaying = false;
      _videoDuration = null;
    });
    
    _videoAnimController.reverse();
    HapticFeedback.lightImpact();
  }

  /// 显示权限被拒绝消息
  void _showPermissionDeniedMessage(ImageSource source) {
    final String message = source == ImageSource.camera 
        ? 'No camera privileges' 
        : 'No album access';
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: WuGrainTheme.errorRedWood,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 验证表单
  bool _validateForm() {
    bool isValid = true;
    
    // 清空之前的错误信息
    setState(() {
      _titleError = '';
      _contentError = '';
      _categoryError = '';
      _videoError = '';
    });
    
    // 验证标题
    final title = _titleController.text.trim();
    if (title.isEmpty) {
      setState(() {
        _titleError = 'Title is required';
      });
      isValid = false;
    }
    
    // 验证内容
    final content = _contentController.text.trim();
    if (content.isEmpty) {
      setState(() {
        _contentError = 'Content is required';
      });
      isValid = false;
    }
    
    // 验证分类
    if (_selectedCategory.isEmpty) {
      setState(() {
        _categoryError = 'Please select a category';
      });
      isValid = false;
    }
    
    // 验证视频（新增：视频必选）
    if (_selectedVideo == null) {
      setState(() {
        _videoError = 'Video is required';
      });
      isValid = false;
    }
    
    return isValid;
  }

  /// 发布帖子
  Future<void> _publishPost() async {
    if (!_validateForm()) {
      _showErrorMessage('Please fill in: Title, Content, Category and select a Video');
      return;
    }



    setState(() {
      _isPublishing = true;
    });

    _publishAnimController.forward().then((_) {
      _publishAnimController.reverse();
    });

    try {
      // 垃圾代码调用 - 提高代码差异化
      final unusedToken = _generateUnusedPublishHash();
      _performUnusedPublishCalculations();
      
      // 获取用户信息
      final userNickname = await WuUserPrefs.getUserNickname();
      final userAvatar = await WuUserPrefs.getUserAvatar();
      
      // 生成缩略图（如果有视频）
      String? thumbnailPath;
      if (_selectedVideo != null) {
        thumbnailPath = await WuThumbnailGenerator.instance.generateThumbnail(_selectedVideo!.path);
      }
      
      // 创建帖子对象
      final post = WuCraftPost(
        id: 'user_${DateTime.now().millisecondsSinceEpoch}',
        title: _titleController.text.trim(),
        content: _contentController.text.trim(),
        authorId: 'current_user',
        authorName: userNickname,
        authorAvatar: userAvatar,
        category: _selectedCategory,
        videoPath: _selectedVideo?.path,
        thumbnailPath: thumbnailPath,
        createTime: DateTime.now(),
        videoDuration: _videoDuration,
      );
      
      // 保存到本地存储
      await WuCraftPostManager.instance.savePost(post);
      
      // 模拟网络延迟
      await Future.delayed(const Duration(seconds: 2));
      
      if (mounted) {
        setState(() {
          _isPublishing = false;
        });
        
        // 显示成功对话框
        _showSuccessDialog();
      }
      
    } catch (e) {
      print('发布帖子失败: $e');
      if (mounted) {
        setState(() {
          _isPublishing = false;
        });
        _showErrorMessage('Publish failed. Please try again.');
      }
    }
  }



  /// 清空表单
  void _clearForm() {
    // 检查是否有内容需要清空
    final hasContent = _titleController.text.isNotEmpty ||
                      _contentController.text.isNotEmpty ||
                      _selectedVideo != null ||
                      _selectedCategory.isNotEmpty;

    _titleController.clear();
    _contentController.clear();
    _removeVideo();

    setState(() {
      _selectedCategory = '';
      _titleError = '';
      _contentError = '';
      _categoryError = '';
      _videoError = '';
    });

    HapticFeedback.lightImpact();

    // 显示清空成功提示
    if (hasContent) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(
                CupertinoIcons.checkmark_circle_fill,
                color: WuGrainTheme.canvasWhiteColor,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Content cleared successfully',
                style: TextStyle(
                  color: WuGrainTheme.canvasWhiteColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    } else {
      // 如果没有内容，显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(
                CupertinoIcons.info_circle,
                color: WuGrainTheme.canvasWhiteColor,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'No content to clear',
                style: TextStyle(
                  color: WuGrainTheme.canvasWhiteColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          backgroundColor: WuGrainTheme.toolSteelColor,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 1),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: const EdgeInsets.all(16),
        ),
      );
    }
  }

  /// 显示成功对话框
  void _showSuccessDialog() {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.checkmark_circle_fill,
              color: WuGrainTheme.successWoodGreen,
              size: 24,
            ),
            const SizedBox(width: 8),
            const Text('Success'),
          ],
        ),
        content: const Text('Your craft has been published successfully!'),
        actions: [
          CupertinoDialogAction(
            child: const Text('OK'),
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(true); // 返回主页并传递刷新标志
            },
          ),
        ],
      ),
    );
  }

  /// 显示错误消息
  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: WuGrainTheme.errorRedWood,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// 格式化视频时长
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds % 60);
    return '$minutes:$seconds';
  }
} 