import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../WoodChips/wu_grain_nav_bar.dart';
import 'wu_timber_stream93.dart';
import 'wu_artisan_hub47.dart';
import 'wu_chisel_cha_vie.dart';
import 'wu_craft_pubish_vie.dart';

/// Wuzo主导航容器 - 整合所有核心页面
class WuMainNavigator52 extends StatefulWidget {
  const WuMainNavigator52({super.key});

  @override
  State<WuMainNavigator52> createState() => _WuMainNavigator52State();
}

class _WuMainNavigator52State extends State<WuMainNavigator52> {
  int _currentIndex = 0;
  late PageController _pageController;
  final GlobalKey<WuTimberStream93State> _timberStreamKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: 0);
  }

  void _onTabTapped(int index) {
    // 如果点击的是发布按钮（索引1），直接跳转到发布页面
    if (index == 1) {
      _navigateToPublishPage();
      return;
    }
    
    // 调整索引：跳过发布按钮
    final realIndex = index > 1 ? index - 1 : index;
    
    setState(() {
      _currentIndex = realIndex;
    });
    
    _pageController.animateToPage(
      realIndex,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  void _navigateToPublishPage() async {
    HapticFeedback.mediumImpact();
    
    final result = await Navigator.of(context).push<bool>(
      CupertinoPageRoute(
        builder: (context) => const WuCraftPublishVie(),
        fullscreenDialog: true,
      ),
    );
    
    // 如果发布成功，刷新主页
    if (result == true && _currentIndex == 0) {
      _timberStreamKey.currentState?.refreshFeed();
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: [
          // 主页视频流
          WuTimberStream93(key: _timberStreamKey),
          
          // AI助手聊天
          const WuChiselChatVie(),
          
          // 个人中心
          const WuArtisanHub47(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: WuGrainTheme.charcoalGrayColor.withOpacity(0.1),
              blurRadius: 16,
              offset: const Offset(0, -4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
          child: WuGrainNavBar(
            currentIndex: _currentIndex == 0 ? 0 : _currentIndex + 1,
            onTap: _onTabTapped,
            items: [
              // 主页
              WuNavItem(
                icon: CupertinoIcons.play_rectangle,
                selectedIcon: CupertinoIcons.play_rectangle_fill,
                label: 'Stream',
              ),
              
              // 发布按钮
              WuNavItem(
                icon: CupertinoIcons.plus_circle,
                selectedIcon: CupertinoIcons.plus_circle_fill,
                label: 'Create',
              ),
              
              // AI助手
              WuNavItem(
                icon: CupertinoIcons.sparkles,
                selectedIcon: CupertinoIcons.sparkles,
                label: 'AI Guide',
              ),
              
              // 个人中心
              WuNavItem(
                icon: CupertinoIcons.person_crop_circle,
                selectedIcon: CupertinoIcons.person_crop_circle_fill,
                label: 'Artisan',
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 发布作品模态框
class WuPublishModal extends StatelessWidget {
  const WuPublishModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: WuGrainTheme.strongShadow,
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: WuGrainTheme.toolSteelColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Text(
                  'Create New Work',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: WuGrainTheme.charcoalGrayColor,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: Icon(
                    CupertinoIcons.xmark_circle_fill,
                    color: WuGrainTheme.toolSteelColor,
                  ),
                ),
              ],
            ),
          ),
          
          // 发布选项
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  _buildPublishOption(
                    icon: CupertinoIcons.video_camera_solid,
                    title: 'Record Video',
                    subtitle: 'Create a new carving video',
                    onTap: () {
                      Navigator.of(context).pop();
                      // 打开录制视频功能
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildPublishOption(
                    icon: CupertinoIcons.folder,
                    title: 'Upload from Gallery',
                    subtitle: 'Select video from your gallery',
                    onTap: () {
                      Navigator.of(context).pop();
                      // 打开图库选择功能
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  _buildPublishOption(
                    icon: CupertinoIcons.doc_text,
                    title: 'Create Tutorial',
                    subtitle: 'Write step-by-step guide',
                    onTap: () {
                      Navigator.of(context).pop();
                      // 打开教程创建功能
                    },
                  ),
                  
                  const Spacer(),
                  
                  // 底部说明
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: WuGrainTheme.pineWoodColor.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          CupertinoIcons.info_circle,
                          size: 20,
                          color: WuGrainTheme.toolSteelColor,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'You have 2 free posts remaining this month',
                            style: TextStyle(
                              fontSize: 12,
                              color: WuGrainTheme.toolSteelColor,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPublishOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: WuGrainTheme.canvasWhiteColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: WuGrainTheme.pineWoodColor,
            width: 1,
          ),
          boxShadow: WuGrainTheme.lightShadow,
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: WuGrainTheme.walnutCarveColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 24,
                color: WuGrainTheme.walnutCarveColor,
              ),
            ),
            
            const SizedBox(width: 16),
            
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: WuGrainTheme.charcoalGrayColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 12,
                      color: WuGrainTheme.toolSteelColor,
                    ),
                  ),
                ],
              ),
            ),
            
            Icon(
              CupertinoIcons.chevron_right,
              size: 16,
              color: WuGrainTheme.toolSteelColor,
            ),
          ],
        ),
      ),
    );
  }
} 