import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../GrainBlue/wu_grain_theme.dart';
import '../WoodChips/wu_3d_logo_anim.dart';
import '../WoodChips/wu_glo_tex.dart';

/// Wuzo奢华启动页 - 木雕创作社区
class WuLuxeSplash89 extends StatefulWidget {
  final VoidCallback? onAnimationComplete;
  final VoidCallback? onGetStarted;
  
  const WuLuxeSplash89({
    super.key,
    this.onAnimationComplete,
    this.onGetStarted,
  });

  @override
  State<WuLuxeSplash89> createState() => _WuLuxeSplash89State();
}

class _WuLuxeSplash89State extends State<WuLuxeSplash89>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _fadeController;
  late AnimationController _buttonController;

  late Animation<double> _backgroundAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _buttonAnimation;
  
  bool _showContent = false;
  bool _showButton = false;
  bool _animationCompleted = false;

  @override
  void initState() {
    super.initState();
    
    // 隐藏状态栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    
    _initAnimations();
    _startSplashSequence();
  }

  void _initAnimations() {
    // 背景渐变动画控制器
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    
    // 整体淡出控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // 按钮动画控制器
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    // 背景渐变动画
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOutSine,
    ));

    // 淡出动画
    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    // 按钮出现动画
    _buttonAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _buttonController,
      curve: Curves.elasticOut,
    ));
  }

  void _startSplashSequence() async {
    // 启动背景动画
    _backgroundController.repeat();
    
    // 延迟显示内容
    await Future.delayed(const Duration(milliseconds: 500));
    if (mounted) {
      setState(() {
        _showContent = true;
      });
    }
    
    // 等待Logo和文字动画完成后显示按钮
    await Future.delayed(const Duration(seconds: 3));
    if (mounted) {
      setState(() {
        _showButton = true;
      });
      _buttonController.forward();
    }
    
    // 自动进入主页（可选）
    await Future.delayed(const Duration(seconds: 2));
    if (mounted && !_animationCompleted) {
      // 这里可以选择是否自动进入，现在只依赖按钮点击
      // _completeAnimation();
    }
  }

  void _completeAnimation() async {
    if (_animationCompleted) return;
    _animationCompleted = true;
    
    // 触觉反馈
    HapticFeedback.mediumImpact();
    
    // 开始淡出
    await _fadeController.forward();
    
    // 恢复状态栏
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    
    // 回调完成
    if (widget.onGetStarted != null) {
      widget.onGetStarted!();
    } else if (widget.onAnimationComplete != null) {
      widget.onAnimationComplete!();
    }
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _fadeController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _backgroundAnimation,
          _fadeAnimation,
          _buttonAnimation,
        ]),
        builder: (context, child) {
          return Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: _buildBackgroundDecoration(),
              child: Stack(
                children: [
                  // 木纹装饰层
                  _buildWoodGrainLayer(),
                  
                  // 主内容层
                  if (_showContent) _buildMainContent(),
                  
                  // 启动按钮
                  if (_showButton) _buildGetStartedButton(),
                  
                  // 底部进度指示器
                  if (_showContent && !_showButton) _buildProgressIndicator(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建动态背景装饰
  BoxDecoration _buildBackgroundDecoration() {
    final progress = _backgroundAnimation.value;
    
    return BoxDecoration(
      gradient: RadialGradient(
        center: Alignment.center,
        radius: 1.5 + (math.sin(progress * 2 * math.pi) * 0.3),
        colors: [
          WuGrainTheme.pineWoodColor.withOpacity(0.9),
          WuGrainTheme.walnutCarveColor.withOpacity(0.8),
          WuGrainTheme.toolSteelColor.withOpacity(0.9),
          WuGrainTheme.charcoalGrayColor,
        ],
        stops: [
          0.0,
          0.4 + (math.cos(progress * 3 * math.pi) * 0.1),
          0.7 + (math.sin(progress * 2 * math.pi) * 0.1),
          1.0,
        ],
      ),
    );
  }



  /// 构建木纹装饰层
  Widget _buildWoodGrainLayer() {
    return Positioned.fill(
      child: CustomPaint(
        painter: WoodGrainPainter(
          progress: _backgroundAnimation.value,
        ),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 3D Logo动画
          Animate(
            effects: [
              ScaleEffect(
                begin: const Offset(0.0, 0.0),
                end: const Offset(1.0, 1.0),
                duration: const Duration(milliseconds: 1200),
                curve: Curves.elasticOut,
              ),
              FadeEffect(
                duration: const Duration(milliseconds: 800),
              ),
            ],
            child: const Wu3DLogoAnim(
              size: 150,
              duration: Duration(seconds: 3),
            ),
          ),
          
          const SizedBox(height: 40),
          
          // 应用名称
          WuGlowText(
            text: 'WUZO',
            style: const TextStyle(
              fontSize: 42,
              fontWeight: FontWeight.bold,
              letterSpacing: 6,
            ),
            duration: const Duration(milliseconds: 1500),
            delay: const Duration(milliseconds: 1200),
            glowColor: WuGrainTheme.walnutCarveColor,
          ),
          
          const SizedBox(height: 16),
          
          // 副标题
          WuGlowText(
            text: 'Wood Carving Community',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w300,
              letterSpacing: 2,
            ),
            duration: const Duration(milliseconds: 1200),
            delay: const Duration(milliseconds: 2000),
            enableTypewriter: false,
            glowColor: WuGrainTheme.toolSteelColor,
          ),
          
          const SizedBox(height: 30),
          
          // 描述文字
          Animate(
            effects: [
              FadeEffect(
                delay: const Duration(milliseconds: 2500),
                duration: const Duration(milliseconds: 1500),
              ),
              SlideEffect(
                delay: const Duration(milliseconds: 2500),
                begin: const Offset(0, 0.3),
                end: const Offset(0, 0),
                curve: Curves.easeOut,
              ),
            ],
            child: WuGlowTextMultiline(
              lines: const [
                'Craft • Create • Share',
                'AI-Powered Guidance',
              ],
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                letterSpacing: 1,
              ),
              lineDuration: const Duration(milliseconds: 1000),
              lineDelay: const Duration(milliseconds: 400),
              enableTypewriter: false,
              enableGlow: false,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建启动按钮
  Widget _buildGetStartedButton() {
    return Positioned(
      bottom: 120,
      left: 0,
      right: 0,
      child: Center(
        child: Transform.scale(
          scale: _buttonAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              boxShadow: [
                BoxShadow(
                  color: WuGrainTheme.walnutCarveColor.withOpacity(0.4),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: WuGrainTheme.canvasWhiteColor.withOpacity(0.3),
                  blurRadius: 10,
                ),
              ],
            ),
            child: ElevatedButton(
              onPressed: _completeAnimation,
              style: ElevatedButton.styleFrom(
                backgroundColor: WuGrainTheme.walnutCarveColor,
                foregroundColor: WuGrainTheme.canvasWhiteColor,
                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Get Started',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: WuGrainTheme.canvasWhiteColor,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建进度指示器
  Widget _buildProgressIndicator() {
    return Animate(
      effects: [
        FadeEffect(
          delay: const Duration(milliseconds: 3000),
          duration: const Duration(milliseconds: 800),
        ),
      ],
      child: Positioned(
        bottom: 80,
        left: 0,
        right: 0,
        child: Center(
          child: Container(
            width: 200,
            height: 4,
            decoration: BoxDecoration(
              color: WuGrainTheme.toolSteelColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(2),
            ),
            child: AnimatedBuilder(
              animation: _backgroundController,
              builder: (context, child) {
                return Container(
                  width: 200 * (_backgroundController.value * 0.8 + 0.2),
                  height: 4,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        WuGrainTheme.walnutCarveColor,
                        WuGrainTheme.canvasWhiteColor,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(2),
                    boxShadow: [
                      BoxShadow(
                        color: WuGrainTheme.walnutCarveColor.withOpacity(0.6),
                        blurRadius: 8,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}



/// 木纹装饰画笔
class WoodGrainPainter extends CustomPainter {
  final double progress;
  
  WoodGrainPainter({required this.progress});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    
    // 绘制木纹线条
    for (int i = 0; i < 8; i++) {
      final path = Path();
      final startY = size.height * 0.1 * i;
      final opacity = 0.1 + (math.sin(progress * 2 * math.pi + i) * 0.05);
      
      paint.color = WuGrainTheme.toolSteelColor.withOpacity(opacity);
      
      path.moveTo(0, startY);
      
      for (double x = 0; x <= size.width; x += 10) {
        final y = startY + math.sin((x / size.width + progress + i * 0.1) * 4 * math.pi) * 20;
        path.lineTo(x, y);
      }
      
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
} 