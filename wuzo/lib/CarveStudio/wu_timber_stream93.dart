import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../main.dart';
import 'package:video_player/video_player.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../WoodChips/wu_vido_crd.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import '../SawdustArchive/wu_mock_data.dart';
import '../WhittleLab/wu_blok_manger.dart';
import '../WhittleLab/wu_craft_pst_mager.dart';
import '../WhittleLab/wu_vido_contller_manaer.dart';
import '../WhittleLab/wu_like_manager.dart';
import 'wu_terms_aeement_dalog.dart';
import 'wu_artisan_cha_chamber.dart';
import 'wu_block_manage_vie27.dart';

/// Wuzo主页视频流 - 木雕作品时间轴
class WuTimberStream93 extends StatefulWidget {
  const WuTimberStream93({super.key});

  @override
  State<WuTimberStream93> createState() => WuTimberStream93State();
}

class WuTimberStream93State extends State<WuTimberStream93> 
    with TickerProviderStateMixin {
  
  late List<CarveBlueprint> _presetPosts;
  late List<WuCraftPost> _userPosts;
  List<CarveBlueprint> _filteredPosts = [];
  late ScrollController _scrollController;
  int _currentIndex = 0;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _initializePage();
    _listenToBlockChanges();
    _listenToUserPostChanges();
    _listenToLikeChanges();
    _setupScrollListener();
  }

  void _initializePage() async {
    // 初始化点赞管理器
    await WuLikeManager.instance.init();

    _presetPosts = WuMockData.mockCarvingPosts;
    _userPosts = WuCraftPostManager.instance.userPosts;

    // 初始化所有帖子的点赞状态
    for (final post in _presetPosts) {
      WuLikeManager.instance.initPostLikeState(post.id, post.isLiked, post.likeCount);
    }
    for (final post in _userPosts) {
      final blueprint = post.toCarveBlueprint();
      WuLikeManager.instance.initPostLikeState(blueprint.id, blueprint.isLiked, blueprint.likeCount);
    }

    _filterPosts();
    setState(() {
      _isInitialized = true;
    });

    // 检查并显示用户协议
    _checkAndShowTermsAgreement();
  }

  /// 检查并显示用户协议
  void _checkAndShowTermsAgreement() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      WuTermsAgreementDialog.showIfNeeded(
        context,
        onAccepted: () {
          print('✅ 用户已同意协议');
        },
        onRejected: () {
          print('❌ 用户拒绝协议，返回启动页');
          // 返回启动页，让用户重新考虑
          WuMainFlow.showSplashAgain();
        },
      );
    });
  }

  void _listenToBlockChanges() {
    WuBlockManager.instance.blockStream.listen((blockedIds) {
      if (mounted) {
        final oldCount = _filteredPosts.length;
        _filterPosts();
        
        // 调整当前索引
        if (_filteredPosts.length < oldCount && _currentIndex >= _filteredPosts.length) {
          setState(() { 
            _currentIndex = _filteredPosts.isEmpty ? 0 : _filteredPosts.length - 1; 
          });
        } else if (_currentIndex >= _filteredPosts.length) {
          setState(() { 
            _currentIndex = 0; 
          });
        }
      }
    });
  }

  void _listenToUserPostChanges() {
    WuCraftPostManager.instance.postStream.listen((userPosts) {
      if (mounted) {
        _userPosts = userPosts;
        _filterPosts();
      }
    });
  }

  void _listenToLikeChanges() {
    WuLikeManager.instance.likeStream.listen((likeStates) {
      if (mounted) {
        // 点赞状态变化时刷新UI
        setState(() {
          // 触发重新构建，让ListView使用最新的点赞状态
        });
        print('🔄 主页监听到点赞状态变化，已刷新UI');
      }
    });
  }

  void _filterPosts() {
    final blockedIds = WuBlockManager.instance.blockedPostIds;
    
    // 合并用户帖子和预设帖子
    final List<CarveBlueprint> allPosts = [];
    
    // 用户帖子排在前面（转换为CarveBlueprint）
    for (final userPost in _userPosts) {
      if (!blockedIds.contains(userPost.id)) {
        allPosts.add(userPost.toCarveBlueprint());
      }
    }
    
    // 预设帖子排在后面
    for (final presetPost in _presetPosts) {
      if (!blockedIds.contains(presetPost.id)) {
        allPosts.add(presetPost);
      }
    }
    
    final oldFilteredPosts = List<CarveBlueprint>.from(_filteredPosts);
    _filteredPosts = allPosts;
    
    // 调整当前索引
    if (_isInitialized && oldFilteredPosts.isNotEmpty && _filteredPosts.length < oldFilteredPosts.length) {
      bool currentPostStillExists = false;
      if (_currentIndex < oldFilteredPosts.length) {
        final currentPost = oldFilteredPosts[_currentIndex];
        final newIndex = _filteredPosts.indexWhere((post) => post.id == currentPost.id);
        if (newIndex != -1) {
          _currentIndex = newIndex;
          currentPostStillExists = true;
        }
      }
      if (!currentPostStillExists && _filteredPosts.isNotEmpty) {
        if (_currentIndex >= _filteredPosts.length) {
          _currentIndex = _filteredPosts.length - 1;
        }
      }
    }
    
    if (mounted) { 
      setState(() {}); 
    }
  }

  void _onBlockPost(CarveBlueprint post) async {
    HapticFeedback.mediumImpact();
    
    final blockedIndex = _filteredPosts.indexWhere((p) => p.id == post.id);
    await WuBlockManager.instance.blockPost(post.id);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Blocked "${post.title}"'),
        backgroundColor: WuGrainTheme.toolSteelColor,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
    
    if (blockedIndex == _currentIndex && _filteredPosts.length > 1) {
      Future.delayed(const Duration(milliseconds: 300), () {
        if (mounted && _filteredPosts.isNotEmpty) {
          final newIndex = _currentIndex >= _filteredPosts.length ? _filteredPosts.length - 1 : _currentIndex;
          _scrollToIndex(newIndex);
        }
      });
    }
  }

  void _onReportPost(CarveBlueprint post) {
    HapticFeedback.mediumImpact();
    _showReportDialog(post);
  }

  /// 显示举报选择对话框
  void _showReportDialog(CarveBlueprint post) {
    showCupertinoModalPopup(
      context: context,
      builder: (context) => WuReportDialog(
        postTitle: post.title,
        onReport: (reason) => _handleReportSubmission(post, reason),
      ),
    );
  }

  /// 处理举报提交
  void _handleReportSubmission(CarveBlueprint post, String reason) async {
    try {
      final reportedIndex = _filteredPosts.indexWhere((p) => p.id == post.id);
      await WuBlockManager.instance.reportPost(post.id, reason);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Report submitted successfully. "${post.title}" has been blocked.'),
            backgroundColor: WuGrainTheme.successWoodGreen,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );

        if (reportedIndex == _currentIndex && _filteredPosts.length > 1) {
          Future.delayed(const Duration(milliseconds: 300), () {
            if (mounted && _filteredPosts.isNotEmpty) {
              final newIndex = _currentIndex >= _filteredPosts.length ? _filteredPosts.length - 1 : _currentIndex;
              _scrollToIndex(newIndex);
            }
          });
        }
      }
    } catch (e) {
      print('举报帖子失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Failed to submit report'),
            backgroundColor: WuGrainTheme.errorRedWood,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _onDeleteUserPost(CarveBlueprint post) async {
    // 确认删除对话框
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Post'),
        content: Text('Are you sure you want to delete "${post.title}"?'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Delete'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await WuCraftPostManager.instance.deletePost(post.id);
      
      HapticFeedback.mediumImpact();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Deleted "${post.title}"'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _onLikePost(CarveBlueprint post) async {
    HapticFeedback.lightImpact();

    // 使用全局点赞管理器处理所有帖子的点赞
    await WuLikeManager.instance.toggleLike(post.id, post.likeCount);

    // 检查是否是用户帖子，如果是则同步更新到用户帖子管理器
    final userPost = _userPosts.firstWhere(
      (userPost) => userPost.id == post.id,
      orElse: () => WuCraftPost(
        id: '',
        title: '',
        content: '',
        authorId: '',
        authorName: '',
        authorAvatar: '',
        category: '',
        createTime: DateTime.now(),
      ),
    );

    if (userPost.id.isNotEmpty) {
      // 获取最新的点赞状态
      final likeState = WuLikeManager.instance.getLikeState(post.id);
      await WuCraftPostManager.instance.updateLikeStatus(
        post.id,
        likeState['isLiked'] ?? false
      );
    }

    // 刷新UI
    _filterPosts();
  }

  void _navigateToDetail(CarveBlueprint post) async {
    final isUserPost = _userPosts.any((userPost) => userPost.id == post.id);

    // 进入详情页前暂停所有主页视频
    WuVideoControllerManager.instance.pauseAllVideos();
    print('🎬 进入详情页，已暂停所有主页视频');

    final result = await Navigator.of(context).push<bool>(
      CupertinoPageRoute(
        builder: (context) => WuVideoDetailPage(
          post: post,
          isUserPost: isUserPost
        ),
      ),
    );
    if (result == true) {
      _filterPosts();
    }
  }

  void _navigateToChat(CarveBlueprint post) {
    Navigator.of(context).push(
      CupertinoPageRoute(
        builder: (context) => WuArtisanChatChamber(artisan: post.creator),
      ),
    );
  }

  void _setupScrollListener() {
    _scrollController.addListener(() {
      if (!_scrollController.hasClients || _filteredPosts.isEmpty) return;
      
      final screenHeight = MediaQuery.of(context).size.height;
      final scrollOffset = _scrollController.offset;
      final itemHeight = screenHeight * 0.7;
      final newIndex = (scrollOffset / itemHeight).round();
      
      if (newIndex >= 0 && newIndex < _filteredPosts.length && newIndex != _currentIndex) {
        final oldIndex = _currentIndex;
        setState(() {
          _currentIndex = newIndex;
        });

        // 滑动切换视频时，暂停之前的视频，播放当前视频
        if (oldIndex < _filteredPosts.length) {
          final oldPost = _filteredPosts[oldIndex];
          WuVideoControllerManager.instance.pauseVideo(oldPost.id);
        }

        if (newIndex < _filteredPosts.length) {
          final currentPost = _filteredPosts[newIndex];
          // 延迟一点播放，确保UI更新完成
          Future.delayed(const Duration(milliseconds: 200), () {
            WuVideoControllerManager.instance.playVideo(currentPost.id);
          });
        }

        print('🎬 滑动切换视频: $oldIndex -> $newIndex');
      }
    });
  }

  void _scrollToIndex(int index) {
    if (!_scrollController.hasClients || index < 0 || index >= _filteredPosts.length) return;
    
    final screenHeight = MediaQuery.of(context).size.height;
    final itemHeight = screenHeight * 0.7;
    final targetOffset = index * itemHeight;
    
    _scrollController.animateTo(
      targetOffset,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    ).then((_) {
      if (mounted) { 
        setState(() { 
          _currentIndex = index; 
        }); 
      }
    });
  }

  /// 公开方法：刷新信息流
  void refreshFeed() {
    _filterPosts();
    if (_filteredPosts.isNotEmpty && _currentIndex >= _filteredPosts.length) {
      setState(() {
        _currentIndex = 0;
      });
      _scrollToIndex(0);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: _buildAppBar(),
      body: _isInitialized ? _buildVideoFeed() : _buildLoadingView(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: WuGrainTheme.walnutCarveColor,
      elevation: 0,
      automaticallyImplyLeading: false,
      title: Row(
        children: [
          Icon(
            CupertinoIcons.hammer_fill,
            color: WuGrainTheme.canvasWhiteColor,
            size: 24,
          ),
          const SizedBox(width: 8),
          const Text(
            'Craft Stream',
            style: TextStyle(
              color: WuGrainTheme.canvasWhiteColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      // actions: [
      //   IconButton(
      //     onPressed: () => _onRefresh(),
      //     icon: Icon(
      //       CupertinoIcons.refresh,
      //       color: WuGrainTheme.canvasWhiteColor,
      //     ),
      //   ),
      // ],
    );
  }

  Widget _buildVideoFeed() {
    if (_filteredPosts.isEmpty) { 
      return _buildEmptyState(); 
    }
    
    return RefreshIndicator(
      onRefresh: _onRefresh,
      backgroundColor: WuGrainTheme.canvasWhiteColor,
      color: WuGrainTheme.walnutCarveColor,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: _filteredPosts.length,
        itemBuilder: (context, index) {
          final originalPost = _filteredPosts[index];
          final isUserPost = _userPosts.any((userPost) => userPost.id == originalPost.id);

          // 使用全局点赞管理器获取最新的点赞状态
          final updatedPost = WuLikeManager.instance.getUpdatedPost(originalPost);

          return WuVideoCard(
            key: ValueKey('video_${updatedPost.id}'),
            post: updatedPost,
            autoPlay: index == _currentIndex,
            onLike: () => _onLikePost(updatedPost),
            onDetailTap: () => _navigateToDetail(updatedPost),
            onAvatarTap: () => _navigateToChat(updatedPost),
            onBlockPost: isUserPost ? null : () => _onBlockPost(updatedPost),
            onReportPost: isUserPost ? null : () => _onReportPost(updatedPost),
            onDeletePost: isUserPost ? () => _onDeleteUserPost(updatedPost) : null,
            isUserPost: isUserPost,
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  WuGrainTheme.walnutCarveColor.withOpacity(0.3),
                  WuGrainTheme.toolSteelColor.withOpacity(0.3),
                ],
              ),
            ),
            child: Icon(
              CupertinoIcons.square_stack_3d_down_right,
              size: 60,
              color: WuGrainTheme.walnutCarveColor,
            ),
          ),
          const SizedBox(height: 24),
          const Text(
            'No Craft Posts',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: WuGrainTheme.charcoalGrayColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Share your first craft creation\nto start your woodworking journey!',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: WuGrainTheme.toolSteelColor,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: WuGrainTheme.luxuryGradient,
              ),
            ),
            child: Center(
              child: SizedBox(
                width: 30,
                height: 30,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    WuGrainTheme.canvasWhiteColor,
                  ),
                  strokeWidth: 3,
                ),
              ),
            ),
          ),
          const SizedBox(height: 20),
          Text(
            'Loading Craft Stream...',
            style: TextStyle(
              fontSize: 16,
              color: WuGrainTheme.toolSteelColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _onRefresh() async {
    _presetPosts = WuMockData.mockCarvingPosts;
    _userPosts = WuCraftPostManager.instance.userPosts;
    _filterPosts();
    setState(() { 
      _currentIndex = 0; 
    });
  }
}

/// 视频详情页面 - 抖音风格全屏播放
class WuVideoDetailPage extends StatefulWidget {
  final CarveBlueprint post;
  final bool isUserPost;
  
  const WuVideoDetailPage({
    super.key,
    required this.post,
    this.isUserPost = false,
  });

  @override
  State<WuVideoDetailPage> createState() => _WuVideoDetailPageState();
}

class _WuVideoDetailPageState extends State<WuVideoDetailPage> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  bool _showControls = true;
  late CarveBlueprint _currentPost;

  @override
  void initState() {
    super.initState();
    // 获取最新的点赞状态
    _currentPost = WuLikeManager.instance.getUpdatedPost(widget.post);
    _initializeVideo();

    // 监听点赞状态变化
    WuLikeManager.instance.likeStream.listen((likeStates) {
      if (mounted && likeStates.containsKey(_currentPost.id)) {
        setState(() {
          _currentPost = WuLikeManager.instance.getUpdatedPost(widget.post);
        });
      }
    });

    // 3秒后自动隐藏控制栏
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _initializeVideo() async {
    if (widget.post.videoUrl.isEmpty) return;
    
    try {
      if (widget.isUserPost) {
        // 用户上传的视频使用文件路径
        _controller = VideoPlayerController.file(File(widget.post.videoUrl));
      } else {
        // 预设视频使用assets路径
        _controller = VideoPlayerController.asset(widget.post.videoUrl);
      }
      
      await _controller.initialize();
      _controller.setLooping(true);
      
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        
        // 自动播放
        _playVideo();
      }
    } catch (e) {
      print('视频初始化失败: $e');
      // 尝试备用方案
      try {
        if (widget.isUserPost) {
          _controller = VideoPlayerController.asset(widget.post.videoUrl);
        } else {
          _controller = VideoPlayerController.file(File(widget.post.videoUrl));
        }
        await _controller.initialize();
        _controller.setLooping(true);
        
        if (mounted) {
          setState(() {
            _isInitialized = true;
          });
          _playVideo();
        }
      } catch (backupError) {
        print('备用方案也失败: $backupError');
      }
    }
  }

  void _playVideo() {
    if (_isInitialized) {
      _controller.play();
      setState(() {
        _isPlaying = true;
      });
    }
  }

  void _pauseVideo() {
    if (_isInitialized) {
      _controller.pause();
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _togglePlay() {
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    // 3秒后自动隐藏控制栏
    if (_showControls && _isPlaying) {
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted && _isPlaying) {
          setState(() {
            _showControls = false;
          });
        }
      });
    }
  }

  /// 点击视频时的处理：切换播放状态和控制栏显示
  void _onVideoTap() {
    if (_isPlaying) {
      // 如果正在播放，暂停并显示控制栏
      _pauseVideo();
      setState(() {
        _showControls = true;
      });
    } else {
      // 如果已暂停，只显示/隐藏控制栏，不自动播放
      // 播放需要点击专门的播放按钮
      setState(() {
        _showControls = !_showControls;
      });

      // 3秒后自动隐藏控制栏
      if (_showControls) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && !_isPlaying) {
            setState(() {
              _showControls = false;
            });
          }
        });
      }
    }
  }

  /// 点赞功能
  void _onLikePost() async {
    HapticFeedback.lightImpact();

    // 使用全局点赞管理器处理点赞
    await WuLikeManager.instance.toggleLike(_currentPost.id, _currentPost.likeCount);

    // 如果是用户帖子，同步更新到用户帖子管理器
    if (widget.isUserPost) {
      final likeState = WuLikeManager.instance.getLikeState(_currentPost.id);
      await WuCraftPostManager.instance.updateLikeStatus(
        _currentPost.id,
        likeState['isLiked'] ?? false
      );
    }

    // 更新当前帖子状态
    setState(() {
      _currentPost = WuLikeManager.instance.getUpdatedPost(widget.post);
    });
  }

  void _onDeleteUserPost() async {
    // 确认删除对话框
    final confirmed = await showCupertinoDialog<bool>(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Delete Post'),
        content: Text('Are you sure you want to delete "${widget.post.title}"?'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            child: const Text('Delete'),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await WuCraftPostManager.instance.deletePost(widget.post.id);
      
      HapticFeedback.mediumImpact();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Deleted "${widget.post.title}"'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
      
      // 返回主页并刷新
      Navigator.of(context).pop(true);
    }
  }

  void _onBlockPost() async {
    HapticFeedback.mediumImpact();
    
    try {
      await WuBlockManager.instance.blockPost(widget.post.id);
      
      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Blocked "${widget.post.title}"'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 2),
        ),
      );
      
      // 返回主页并刷新
      Navigator.of(context).pop(true);
      
    } catch (e) {
      print('屏蔽帖子失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Failed to block post'),
          backgroundColor: WuGrainTheme.errorRedWood,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _onReportPost() {
    // 使用统一的举报对话框
    showCupertinoModalPopup(
      context: context,
      builder: (context) => WuReportDialog(
        postTitle: widget.post.title,
        onReport: _reportPost,
      ),
    );
  }

  void _reportPost(String reason) async {
    HapticFeedback.mediumImpact();
    
    try {
      await WuBlockManager.instance.reportPost(widget.post.id, reason);
      
      // 显示举报成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Report submitted successfully. Post has been blocked.'),
          backgroundColor: WuGrainTheme.successWoodGreen,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
        ),
      );
      
      // 返回主页并刷新
      Navigator.of(context).pop(true);
      
    } catch (e) {
      print('举报帖子失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Failed to submit report'),
          backgroundColor: WuGrainTheme.errorRedWood,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _onVideoTap,
        child: Stack(
          children: [
            // 全屏视频背景
            if (_isInitialized)
              Positioned.fill(
                child: AspectRatio(
                  aspectRatio: _controller.value.aspectRatio,
                  child: VideoPlayer(_controller),
                ),
              )
            else
              Positioned.fill(
                child: Container(
                  color: Colors.black,
                  child: Center(
                    child: CircularProgressIndicator(
                      color: WuGrainTheme.canvasWhiteColor,
                    ),
                  ),
                ),
              ),

            // 中央播放按钮（暂停时显示）
            if (_isInitialized && !_isPlaying)
              Positioned.fill(
                child: Center(
                  child: GestureDetector(
                    onTap: () {
                      _playVideo();
                    },
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.6),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Icon(
                        CupertinoIcons.play_fill,
                        color: WuGrainTheme.canvasWhiteColor,
                        size: 40,
                      ),
                    ),
                  ),
                ),
              ),
            
            // 顶部控制栏 (返回按钮等)
            if (_showControls)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.only(
                    top: MediaQuery.of(context).padding.top + 8,
                    left: 16,
                    right: 16,
                    bottom: 16,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withOpacity(0.5),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            CupertinoIcons.back,
                            color: WuGrainTheme.canvasWhiteColor,
                            size: 24,
                          ),
                        ),
                      ),
                      const Spacer(),
                      // 更多选项
                      GestureDetector(
                        onTap: _showMoreOptions,
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.3),
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            CupertinoIcons.ellipsis_vertical,
                            color: WuGrainTheme.canvasWhiteColor,
                            size: 24,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            
            // 右侧操作按钮
            if (_showControls)
              Positioned(
                right: 16,
                bottom: 120,
                child: Column(
                  children: [
                    // 点赞按钮
                    _buildActionButton(
                      icon: _currentPost.isLiked
                          ? CupertinoIcons.heart_fill
                          : CupertinoIcons.heart,
                      label: _formatCount(_currentPost.likeCount),
                      isActive: _currentPost.isLiked,
                      onTap: _onLikePost,
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // // 分享按钮
                    // _buildActionButton(
                    //   icon: CupertinoIcons.share,
                    //   label: 'Share',
                    //   isActive: false,
                    //   onTap: () {},
                    // ),
                  ],
                ),
              ),
            
            // 底部作品信息
            if (_showControls)
              Positioned(
                left: 16,
                right: 100,
                bottom: 60,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 创作者信息
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).push(
                          CupertinoPageRoute(
                            builder: (context) => WuArtisanChatChamber(artisan: widget.post.creator),
                          ),
                        );
                      },
                      child: Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: WuGrainTheme.canvasWhiteColor,
                                width: 2,
                              ),
                            ),
                            child: ClipOval(
                              child: widget.post.creator.avatarUrl.startsWith('http')
                                  ? CachedNetworkImage(
                                      imageUrl: widget.post.creator.avatarUrl,
                                      fit: BoxFit.cover,
                                      placeholder: (context, url) => Container(
                                        color: WuGrainTheme.pineWoodColor,
                                        child: Icon(
                                          CupertinoIcons.person,
                                          color: WuGrainTheme.toolSteelColor,
                                          size: 20,
                                        ),
                                      ),
                                      errorWidget: (context, url, error) => Container(
                                        color: WuGrainTheme.walnutCarveColor,
                                        child: Icon(
                                          CupertinoIcons.person,
                                          color: WuGrainTheme.canvasWhiteColor,
                                          size: 20,
                                        ),
                                      ),
                                    )
                                  : widget.post.creator.avatarUrl.startsWith('/')
                                  ? Image.file(
                                      File(widget.post.creator.avatarUrl),
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Container(
                                          color: WuGrainTheme.walnutCarveColor,
                                          child: Icon(
                                            CupertinoIcons.person,
                                            color: WuGrainTheme.canvasWhiteColor,
                                            size: 20,
                                          ),
                                        );
                                      },
                                  )
                                  : Image.asset(
                                      widget.post.creator.avatarUrl,
                                      fit: BoxFit.cover,
                                      errorBuilder: (context, error, stackTrace) {
                                        return Container(
                                          color: WuGrainTheme.walnutCarveColor,
                                          child: Icon(
                                            CupertinoIcons.person,
                                            color: WuGrainTheme.canvasWhiteColor,
                                            size: 20,
                                          ),
                                        );
                                      },
                                  ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.post.creator.nickname,
                                  style: const TextStyle(
                                    color: WuGrainTheme.canvasWhiteColor,
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                if (widget.post.creator.isVerified)
                                  Row(
                                    children: [
                                      Icon(
                                        CupertinoIcons.checkmark_seal_fill,
                                        color: WuGrainTheme.walnutCarveColor,
                                        size: 14,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        'Verified Artisan',
                                        style: TextStyle(
                                          color: WuGrainTheme.canvasWhiteColor.withOpacity(0.8),
                                          fontSize: 12,
                                        ),
                                      ),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // 作品标题和描述
                    Text(
                      widget.post.title,
                      style: const TextStyle(
                        color: WuGrainTheme.canvasWhiteColor,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.post.description,
                      style: TextStyle(
                        color: WuGrainTheme.canvasWhiteColor.withOpacity(0.9),
                        fontSize: 14,
                      ),
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // 标签
                    Wrap(
                      spacing: 4,
                      children: widget.post.tags.take(3).map((tag) {
                        return Text(
                          '#$tag ',
                          style: TextStyle(
                            color: WuGrainTheme.walnutCarveColor,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            
            // 底部进度条
            if (_isInitialized && _showControls)
              Positioned(
                bottom: 40,
                left: 16,
                right: 16,
                child: VideoProgressIndicator(
                  _controller,
                  allowScrubbing: true,
                  colors: VideoProgressColors(
                    playedColor: WuGrainTheme.walnutCarveColor,
                    backgroundColor: Colors.white.withOpacity(0.3),
                    bufferedColor: Colors.white.withOpacity(0.5),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showMoreOptions() {
    final List<Widget> actions = [];
    
    if (widget.isUserPost) {
      // 用户自己的帖子显示删除选项
      actions.add(
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            _onDeleteUserPost();
          },
          child: Text(
            'Delete Post',
            style: TextStyle(color: WuGrainTheme.errorRedWood),
          ),
        ),
      );
    } else {
      // 其他人的帖子显示屏蔽和举报选项
      actions.addAll([
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            _onBlockPost();
          },
          child: const Text('Block Post'),
        ),
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            _onReportPost();
          },
          child: Text(
            'Report Post',
            style: TextStyle(color: WuGrainTheme.errorRedWood),
          ),
        ),
      ]);
    }

    if (actions.isEmpty) return;

    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(widget.isUserPost ? 'Your Post Options' : 'Post Options'),
        actions: actions,
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  /// 构建右侧操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required bool isActive,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: isActive 
                  ? WuGrainTheme.walnutCarveColor 
                  : WuGrainTheme.canvasWhiteColor,
              size: 24,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              color: WuGrainTheme.canvasWhiteColor,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }
}

/// 举报对话框组件
class WuReportDialog extends StatefulWidget {
  final String postTitle;
  final Function(String reason) onReport;

  const WuReportDialog({
    Key? key,
    required this.postTitle,
    required this.onReport,
  }) : super(key: key);

  @override
  State<WuReportDialog> createState() => _WuReportDialogState();
}

class _WuReportDialogState extends State<WuReportDialog> {
  String? _selectedReason;

  final List<String> _reportReasons = [
    'Inappropriate Content',
    'Spam',
    'Harassment',
    'False Information',
    'Other',
  ];

  void _onReasonSelected(String reason) {
    setState(() {
      _selectedReason = reason;
    });
  }

  void _submitReport() {
    final finalReason = _selectedReason ?? '';

    if (finalReason.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Please select a reason'),
          backgroundColor: WuGrainTheme.errorRedWood,
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    Navigator.of(context).pop();
    widget.onReport(finalReason);
  }

  @override
  Widget build(BuildContext context) {
    // 构建选项列表
    List<Widget> actionWidgets = [];

    // 添加举报原因选项
    for (String reason in _reportReasons) {
      actionWidgets.add(
        CupertinoActionSheetAction(
          onPressed: () => _onReasonSelected(reason),
          child: Row(
            children: [
              Icon(
                _selectedReason == reason
                    ? CupertinoIcons.checkmark_circle_fill
                    : CupertinoIcons.circle,
                color: _selectedReason == reason
                    ? WuGrainTheme.walnutCarveColor
                    : WuGrainTheme.toolSteelColor,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  reason,
                  style: TextStyle(
                    color: _selectedReason == reason
                        ? WuGrainTheme.walnutCarveColor
                        : WuGrainTheme.charcoalGrayColor,
                    fontWeight: _selectedReason == reason
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return CupertinoActionSheet(
      title: const Text(
        'Report Post',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
      message: Text('Why are you reporting "${widget.postTitle}"?'),
      actions: actionWidgets,
      cancelButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 提交按钮（如果已选择原因）
          if (_selectedReason != null)
            CupertinoActionSheetAction(
              onPressed: _submitReport,
              child: Text(
                'Submit Report',
                style: TextStyle(
                  color: WuGrainTheme.errorRedWood,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          // 取消按钮
          CupertinoActionSheetAction(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}