import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import '../GrainBlue/wu_grain_theme.dart';
import '../WhittleLab/wu_uer_prefs.dart';
import '../WhittleLab/wu_craft_pst_mager.dart';

/// Wuzo头像昵称编辑页面
class WuProfileEditPage extends StatefulWidget {
  final String currentNickname;
  final String currentAvatar;
  
  const WuProfileEditPage({
    super.key,
    required this.currentNickname,
    required this.currentAvatar,
  });

  @override
  State<WuProfileEditPage> createState() => _WuProfileEditPageState();
}

class _WuProfileEditPageState extends State<WuProfileEditPage> {
  late TextEditingController _nicknameController;
  String _selectedAvatar = '';
  
  // 可选择的头像列表（使用下载的头像）
  final List<String> _availableAvatars = [
    'assets/avatars/avatar_chen.jpg',
    'assets/avatars/avatar_alex.jpg',
    'assets/avatars/avatar_sarah.jpg',
    'assets/avatars/avatar_mike.jpg',
    'assets/avatars/avatar_emma.jpg',
    'assets/avatars/avatar_tom.jpg',
    'assets/avatars/avatar_jin.jpg',
    'assets/avatars/avatar_lisa.jpg',
  ];

  @override
  void initState() {
    super.initState();
    _nicknameController = TextEditingController(text: widget.currentNickname);
    _selectedAvatar = widget.currentAvatar;
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    super.dispose();
  }

  void _saveChanges() async {
    final nickname = _nicknameController.text.trim();
    
    if (nickname.isEmpty) {
      _showErrorDialog('Nickname cannot be empty');
      return;
    }
    
    if (nickname.length < 2) {
      _showErrorDialog('Nickname must be at least 2 characters');
      return;
    }
    
    if (nickname.length > 20) {
      _showErrorDialog('Nickname cannot exceed 20 characters');
      return;
    }
    
    // 保存到本地存储
    await WuUserPrefs.saveUserNickname(nickname);
    await WuUserPrefs.saveUserAvatar(_selectedAvatar);

    // 同步更新所有用户作品的作者信息
    await WuCraftPostManager.instance.updateAuthorInfo(nickname, _selectedAvatar);

    // 触觉反馈
    HapticFeedback.mediumImpact();

    // 返回结果
    if (mounted) {
      Navigator.of(context).pop({
        'nickname': nickname,
        'avatar': _selectedAvatar,
      });
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// 下载头像到相册
  Future<void> _downloadAvatarToGallery(String avatarPath) async {
    try {
      // 请求相册权限
      final permission = await Permission.photos.request();
      if (permission != PermissionStatus.granted) {
        _showErrorDialog('Permission denied. Please allow photo library access in Settings.');
        return;
      }

      // 加载asset图片
      final ByteData data = await rootBundle.load(avatarPath);
      final Uint8List bytes = data.buffer.asUint8List();

      // 保存到相册
      final result = await ImageGallerySaver.saveImage(
        bytes,
        quality: 100,
        name: "wuzo_avatar_${DateTime.now().millisecondsSinceEpoch}",
      );

      if (result['isSuccess'] == true) {
        HapticFeedback.mediumImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Avatar saved to photo library successfully!'),
            backgroundColor: WuGrainTheme.successWoodGreen,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 2),
          ),
        );
      } else {
        _showErrorDialog('Failed to save avatar to photo library');
      }
    } catch (e) {
      print('Error saving avatar: $e');
      _showErrorDialog('Failed to save avatar: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      appBar: AppBar(
        backgroundColor: WuGrainTheme.walnutCarveColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(
            CupertinoIcons.back,
            color: WuGrainTheme.canvasWhiteColor,
          ),
        ),
        title: Text(
          'Edit Profile',
          style: TextStyle(
            color: WuGrainTheme.canvasWhiteColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          TextButton(
            onPressed: _saveChanges,
            child: Text(
              'Save',
              style: TextStyle(
                color: WuGrainTheme.canvasWhiteColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头像选择区域
            _buildAvatarSection(),
            
            const SizedBox(height: 30),
            
            // 昵称编辑区域
            _buildNicknameSection(),
            
            const SizedBox(height: 40),
            
            // 保存按钮
            _buildSaveButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: WuGrainTheme.mediumShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                CupertinoIcons.photo_camera,
                color: WuGrainTheme.walnutCarveColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Choose Avatar',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: WuGrainTheme.charcoalGrayColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 当前选中的头像预览
          Center(
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: WuGrainTheme.walnutCarveColor,
                  width: 3,
                ),
                boxShadow: WuGrainTheme.mediumShadow,
              ),
              child: CircleAvatar(
                radius: 50,
                backgroundImage: AssetImage(_selectedAvatar),
                backgroundColor: WuGrainTheme.pineWoodColor,
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // 头像选择网格
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _availableAvatars.length,
            itemBuilder: (context, index) {
              final avatar = _availableAvatars[index];
              final isSelected = avatar == _selectedAvatar;
              
              return Stack(
                clipBehavior: Clip.none,
                children: [
                  // 头像主体
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      setState(() {
                        _selectedAvatar = avatar;
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected
                              ? WuGrainTheme.walnutCarveColor
                              : WuGrainTheme.pineWoodColor,
                          width: isSelected ? 3 : 1,
                        ),
                        boxShadow: isSelected
                            ? WuGrainTheme.lightShadow
                            : null,
                      ),
                      child: CircleAvatar(
                        backgroundImage: AssetImage(avatar),
                        backgroundColor: WuGrainTheme.pineWoodColor,
                      ),
                    ),
                  ),

                  // 下载按钮 - 悬浮在右下角
                  Positioned(
                    bottom: -4,
                    right: -4,
                    child: GestureDetector(
                      onTap: () => _downloadAvatarToGallery(avatar),
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: WuGrainTheme.walnutCarveColor,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: WuGrainTheme.canvasWhiteColor,
                            width: 1.5,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.25),
                              blurRadius: 3,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: Icon(
                          CupertinoIcons.arrow_down_to_line,
                          color: WuGrainTheme.canvasWhiteColor,
                          size: 10,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNicknameSection() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(20),
        boxShadow: WuGrainTheme.mediumShadow,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                CupertinoIcons.textformat,
                color: WuGrainTheme.walnutCarveColor,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Nickname',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: WuGrainTheme.charcoalGrayColor,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          TextField(
            controller: _nicknameController,
            maxLength: 20,
            decoration: InputDecoration(
              hintText: 'Enter your nickname',
              hintStyle: TextStyle(
                color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
              ),
              filled: true,
              fillColor: WuGrainTheme.pineWoodColor.withOpacity(0.3),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: WuGrainTheme.walnutCarveColor,
                  width: 2,
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            style: TextStyle(
              fontSize: 16,
              color: WuGrainTheme.charcoalGrayColor,
              fontWeight: FontWeight.w500,
            ),
            onChanged: (value) {
              // 实时更新字符计数
              setState(() {});
            },
          ),
          
          const SizedBox(height: 8),
          
          Text(
            'Tip: Choose a unique nickname that represents your woodworking style',
            style: TextStyle(
              fontSize: 12,
              color: WuGrainTheme.toolSteelColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    final isValid = _nicknameController.text.trim().length >= 2;
    
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isValid ? _saveChanges : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: isValid 
              ? WuGrainTheme.walnutCarveColor 
              : WuGrainTheme.toolSteelColor.withOpacity(0.3),
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: isValid ? 8 : 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.checkmark_circle,
              color: WuGrainTheme.canvasWhiteColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Save Changes',
              style: TextStyle(
                color: WuGrainTheme.canvasWhiteColor,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }
} 