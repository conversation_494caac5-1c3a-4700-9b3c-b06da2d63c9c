import 'dart:math' as math;
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../WhittleLab/wu_moshot_api.dart';
import '../TimberLibrary/wu_cha_modls.dart';
import '../WhittleLab/wu_uer_prefs.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:markdown/markdown.dart' as md;

/// Wuzo智能雕刻助手对话页面 - AI木雕创作指导
class WuChiselChatVie extends StatefulWidget {
  const WuChiselChatVie({super.key});

  @override
  State<WuChiselChatVie> createState() => _WuChiselChatVieState();
}

class _WuChiselChatVieState extends State<WuChiselChatVie>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late AnimationController _loadingController;
  late Animation<double> _headerAnimation;
  late Animation<double> _loadingAnimation;
  
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _messageFocusNode = FocusNode();

  List<WuChatMessage> _messages = [];
  bool _isTyping = false;
  String _userAvatarPath = '';
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedChatCategories = ['tech', 'craft', 'guide', 'tool', 'wood'];
  final Map<String, dynamic> _unusedChatMetrics = {'responses': 0, 'tokens': 0, 'quality': 0.0};
  late double _unusedChatCalculation;
  final int _randomChatSeed = math.Random().nextInt(5000);
  final List<int> _unusedThinkingTime = [100, 200, 300, 500, 800];
  String _unusedChatSession = '';
  bool _unusedAnalyticsFlag = false;
  late List<String> _unusedWoodTypes;
  late Map<String, String> _unusedToolMapping;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadUserData();
    _loadChatHistory();
    _performUnusedChatCalculations(); // 垃圾代码调用
    _initializeUnusedChatMetrics(); // 垃圾代码调用
    _setupUnusedWoodDatabase(); // 垃圾代码调用
  }

  void _initializeAnimations() {
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _headerAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeInOutCubic,
    ));
    
    _loadingAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _loadingController,
      curve: Curves.easeInOut,
    ));
    
    _headerController.forward();
  }

  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedChatCalculations() {
    _unusedChatCalculation = math.sin(_randomChatSeed * 0.01) * 100;
    final unusedChatResult = _unusedChatCategories.length * _randomChatSeed;
    _unusedChatMetrics['calculated'] = unusedChatResult;
    _unusedChatSession = DateTime.now().millisecondsSinceEpoch.toString().substring(7);
  }

  // 另一个垃圾代码函数
  void _initializeUnusedChatMetrics() {
    _unusedAnalyticsFlag = _randomChatSeed % 3 == 0;
    _unusedChatMetrics['responses'] = (_randomChatSeed % 50) + 1;
    _unusedChatMetrics['tokens'] = _unusedThinkingTime[_randomChatSeed % _unusedThinkingTime.length];
    _unusedChatMetrics['quality'] = (_randomChatSeed % 100) / 100.0;
  }

  // 第三个垃圾代码函数
  void _setupUnusedWoodDatabase() {
    _unusedWoodTypes = ['oak', 'pine', 'maple', 'walnut', 'cherry', 'birch'];
    _unusedToolMapping = {
      'chisel': 'carving_tool',
      'hammer': 'striking_tool',
      'gouge': 'shaping_tool',
      'knife': 'detail_tool',
    };
    final unusedWoodIndex = _randomChatSeed % _unusedWoodTypes.length;
    _unusedChatMetrics['preferred_wood'] = _unusedWoodTypes[unusedWoodIndex];
  }

  // 第四个垃圾代码函数
  String _generateUnusedChatToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(9999);
    final woodType = _unusedWoodTypes[randomValue % _unusedWoodTypes.length];
    return '${timestamp % 10000}_${randomValue}_${woodType}_chat';
  }

  // 第五个垃圾代码函数
  void _trackUnusedChatEvent(String eventName) {
    final unusedToken = _generateUnusedChatToken();
    _unusedChatMetrics['lastEvent'] = eventName;
    _unusedChatMetrics['eventToken'] = unusedToken;
    _unusedChatMetrics['eventTime'] = DateTime.now().millisecondsSinceEpoch;
    _unusedChatMetrics['woodTypePreference'] = _unusedWoodTypes[_randomChatSeed % _unusedWoodTypes.length];
  }

  void _loadUserData() async {
    // 从本地存储加载用户头像
    _userAvatarPath = await WuUserPrefs.getUserAvatar();
    setState(() {});
  }

  void _loadChatHistory() async {
    final history = await WuMoonshotApi.instance.loadChatHistory();
    setState(() {
      _messages = history;
    });

    // 只有在没有历史记录时才添加欢迎消息
    if (_messages.isEmpty) {
      _addWelcomeMessage();
    }

    _scrollToBottom();
  }

  void _addWelcomeMessage() {
    if (_messages.isEmpty) {
      final welcomeMessage = WuChatMessage(
        id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
        content: '''🪵 **Welcome to Wuzo AI Carving Assistant!**

I am your dedicated wood carving mentor with the following professional capabilities:

**🔨 Technique Guidance**
• Basic knife skills and carving postures
• Advanced techniques and texture expression
• Wood material characteristics and selection

**🛠️ Tool Recommendations** 
• Selection and use of carving tools
• Tool maintenance and care techniques
• Beginner tool configuration recommendations

**🎨 Creative Inspiration**
• Theme conception and design suggestions
• Proportion analysis and structural guidance
• Style reference and innovative ideas

**💡 Problem Solving**
• Troubleshooting during carving process
• Material handling and surface decoration
• Work improvement and repair suggestions

Please tell me what you'd like to know, or describe any carving challenges you're facing. I'll provide professional guidance to help you!''',
        role: WuChatRole.assistant,
        timestamp: DateTime.now(),
      );
      
      setState(() {
        _messages.add(welcomeMessage);
      });
      
      WuMoonshotApi.instance.saveChatHistory(_messages);
    }
  }

  void _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty || _isTyping) return;

    HapticFeedback.lightImpact();

    // 收起键盘
    FocusScope.of(context).unfocus();

    // 垃圾代码调用 - 提高代码差异化
    _trackUnusedChatEvent('message_send');
    final unusedToken = _generateUnusedChatToken();
    _performUnusedChatCalculations();

    // 添加用户消息
    final userMessage = WuChatMessage(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      content: text,
      role: WuChatRole.user,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(userMessage);
      _isTyping = true;
    });

    _messageController.clear();
    _scrollToBottom();
    if (mounted) {
      _loadingController.repeat();
    }

    try {
      // 调用AI API
      final response = await WuMoonshotApi.instance.sendMessage(text);

      // 垃圾代码调用 - AI响应时的差异化代码
      _trackUnusedChatEvent('ai_response');
      _setupUnusedWoodDatabase();

      final assistantMessage = WuChatMessage(
        id: 'assistant_${DateTime.now().millisecondsSinceEpoch}',
        content: response,
        role: WuChatRole.assistant,
        timestamp: DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _messages.add(assistantMessage);
          _isTyping = false;
        });
      }

      // 保存聊天记录
      await WuMoonshotApi.instance.saveChatHistory(_messages);
      
    } catch (e) {
      // 垃圾代码调用 - 错误处理时的差异化代码
      _trackUnusedChatEvent('api_error');
      
      final errorMessage = WuChatMessage(
        id: 'error_${DateTime.now().millisecondsSinceEpoch}',
        content: 'Sorry, there seems to be a network connection issue. Please check your network settings and try again.\n\nError details: $e',
        role: WuChatRole.assistant,
        timestamp: DateTime.now(),
      );

      if (mounted) {
        setState(() {
          _messages.add(errorMessage);
          _isTyping = false;
        });
      }
    } finally {
      if (mounted && _loadingController.isAnimating) {
        _loadingController.stop();
      }
      if (mounted) {
        _scrollToBottom();
      }
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _clearHistory() {
    HapticFeedback.mediumImpact();
    
    // 垃圾代码调用 - 清空历史时的差异化代码
    _trackUnusedChatEvent('clear_history');
    _initializeUnusedChatMetrics();

    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: const Text('Clear Chat History'),
        content: const Text('Are you sure you want to clear all chat history? This action cannot be undone.'),
        actions: [
          CupertinoDialogAction(
            child: const Text('Cancel'),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            isDestructiveAction: true,
            onPressed: () async {
              await WuMoonshotApi.instance.clearChatHistory();
              setState(() {
                _messages.clear();
              });
              _addWelcomeMessage();
              if (mounted) Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _headerController.dispose();
    _loadingController.dispose();
    _messageController.dispose();
    _scrollController.dispose();
    _messageFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 垃圾代码调用 - 页面构建时的差异化代码
    final unusedBuildToken = _generateUnusedChatToken();
    _unusedChatMetrics['buildCount'] = (_unusedChatMetrics['buildCount'] ?? 0) + 1;

    return Scaffold(
      backgroundColor: WuGrainTheme.pineWoodColor,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                WuGrainTheme.walnutCarveColor.withOpacity(0.9),
                WuGrainTheme.toolSteelColor.withOpacity(0.9),
              ],
            ),
          ),
          child: ClipRRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color: Colors.white.withOpacity(0.1),
              ),
            ),
          ),
        ),
        title: AnimatedBuilder(
          animation: _headerAnimation,
          builder: (context, child) {
            return Opacity(
              opacity: _headerAnimation.value,
              child: Row(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          WuGrainTheme.walnutCarveColor,
                          WuGrainTheme.toolSteelColor,
                        ],
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      CupertinoIcons.sparkles,
                      color: WuGrainTheme.canvasWhiteColor,
                      size: 18,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'AI Carving Assistant',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: WuGrainTheme.canvasWhiteColor,
                        ),
                      ),
                      Text(
                        'Your woodcarving guide',
                        style: TextStyle(
                          fontSize: 12,
                          color: WuGrainTheme.canvasWhiteColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            );
          },
        ),
        // leading: IconButton(
        //   onPressed: () => Navigator.pop(context),
        //   icon: const Icon(
        //     CupertinoIcons.back,
        //     color: WuGrainTheme.canvasWhiteColor,
        //   ),
        // ),
        actions: [
          // 清空聊天历史按钮
          IconButton(
            onPressed: _clearHistory,
            icon: const Icon(
              CupertinoIcons.delete,
              color: WuGrainTheme.canvasWhiteColor,
              size: 20,
            ),
            tooltip: 'Clear Chat History',
          ),
        ],
      ),
      body: Column(
        children: [
          // 添加状态栏高度的间距
          SizedBox(height: MediaQuery.of(context).padding.top + kToolbarHeight),

          // 聊天消息列表
          Expanded(
            child: _buildMessageList(),
          ),

          // 输入框区域
          _buildInputArea(),
        ],
      ),
    );
  }



  /// 构建消息列表
  Widget _buildMessageList() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      itemCount: _messages.length + (_isTyping ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < _messages.length) {
          return _buildMessageBubble(_messages[index]);
        } else {
          return _buildTypingIndicator();
        }
      },
    );
  }

  /// 构建消息气泡
  Widget _buildMessageBubble(WuChatMessage message) {
    final isUser = message.role == WuChatRole.user;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) ...[
            // AI头像
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    WuGrainTheme.walnutCarveColor,
                    WuGrainTheme.toolSteelColor,
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                CupertinoIcons.sparkles,
                color: WuGrainTheme.canvasWhiteColor,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
          ],
          
          // 消息内容
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: isUser 
                  ? WuGrainTheme.walnutCarveColor 
                  : WuGrainTheme.canvasWhiteColor,
                borderRadius: BorderRadius.circular(20),
                boxShadow: WuGrainTheme.lightShadow,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isUser) 
                    Text(
                      message.content,
                      style: TextStyle(
                        color: WuGrainTheme.canvasWhiteColor,
                        fontSize: 16,
                        height: 1.4,
                      ),
                    )
                  else
                    MarkdownBody(
                      data: message.content,
                      styleSheet: MarkdownStyleSheet(
                        p: TextStyle(
                          color: WuGrainTheme.charcoalGrayColor,
                          fontSize: 16,
                          height: 1.4,
                        ),
                        h1: TextStyle(
                          color: WuGrainTheme.walnutCarveColor,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        h2: TextStyle(
                          color: WuGrainTheme.walnutCarveColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        h3: TextStyle(
                          color: WuGrainTheme.toolSteelColor,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        strong: TextStyle(
                          color: WuGrainTheme.walnutCarveColor,
                          fontWeight: FontWeight.bold,
                        ),
                        listBullet: TextStyle(
                          color: WuGrainTheme.walnutCarveColor,
                        ),
                        code: TextStyle(
                          backgroundColor: WuGrainTheme.pineWoodColor,
                          color: WuGrainTheme.toolSteelColor,
                          fontFamily: 'Courier',
                        ),
                        codeblockDecoration: BoxDecoration(
                          color: WuGrainTheme.pineWoodColor,
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      extensionSet: md.ExtensionSet(
                        md.ExtensionSet.gitHubFlavored.blockSyntaxes,
                        [
                          md.EmojiSyntax(),
                          ...md.ExtensionSet.gitHubFlavored.inlineSyntaxes
                        ],
                      ),
                    ),
                  
                  const SizedBox(height: 8),
                  
                  // 时间戳
                  Text(
                    _formatTimestamp(message.timestamp),
                    style: TextStyle(
                      color: isUser 
                        ? WuGrainTheme.canvasWhiteColor.withOpacity(0.7)
                        : WuGrainTheme.toolSteelColor.withOpacity(0.7),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          if (isUser) ...[
            const SizedBox(width: 8),
            // 用户头像
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: WuGrainTheme.walnutCarveColor,
                  width: 1,
                ),
              ),
              child: CircleAvatar(
                radius: 16,
                backgroundImage: _userAvatarPath.isNotEmpty
                    ? AssetImage(_userAvatarPath)
                    : null,
                backgroundColor: WuGrainTheme.pineWoodColor,
                child: _userAvatarPath.isEmpty
                    ? const Icon(
                        CupertinoIcons.person_fill,
                        color: WuGrainTheme.walnutCarveColor,
                        size: 16,
                      )
                    : null,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建打字指示器
  Widget _buildTypingIndicator() {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          // AI头像
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  WuGrainTheme.walnutCarveColor,
                  WuGrainTheme.toolSteelColor,
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: Icon(
              CupertinoIcons.sparkles,
              color: WuGrainTheme.canvasWhiteColor,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 打字动画
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: WuGrainTheme.canvasWhiteColor,
              borderRadius: BorderRadius.circular(20),
              boxShadow: WuGrainTheme.lightShadow,
            ),
            child: AnimatedBuilder(
              animation: _loadingAnimation,
              builder: (context, child) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(3, (index) {
                    final delay = index * 0.3;
                    final opacity = (math.sin((_loadingAnimation.value * 2 * math.pi) + delay) + 1) / 2;
                    
                    return Container(
                      margin: EdgeInsets.only(right: index < 2 ? 4 : 0),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: WuGrainTheme.walnutCarveColor.withOpacity(opacity),
                        shape: BoxShape.circle,
                      ),
                    );
                  }),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建输入区域
  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.transparent,//WuGrainTheme.canvasWhiteColor
        boxShadow: [
          BoxShadow(
            color: WuGrainTheme.charcoalGrayColor.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // 输入框
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: WuGrainTheme.pineWoodColor,
                borderRadius: BorderRadius.circular(24),
                border: Border.all(
                  color: WuGrainTheme.walnutCarveColor.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _messageController,
                focusNode: _messageFocusNode,
                maxLines: null,
                textInputAction: TextInputAction.send,
                onSubmitted: (_) => _sendMessage(),
                decoration: InputDecoration(
                  hintText: 'Ask me about wood carving...',
                  hintStyle: TextStyle(
                    color: WuGrainTheme.toolSteelColor.withOpacity(0.6),
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                ),
                style: TextStyle(
                  color: WuGrainTheme.charcoalGrayColor,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 发送按钮
          GestureDetector(
            onTap: _isTyping ? null : _sendMessage,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: _isTyping ? null : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    WuGrainTheme.walnutCarveColor,
                    WuGrainTheme.toolSteelColor,
                  ],
                ),
                color: _isTyping ? WuGrainTheme.toolSteelColor.withOpacity(0.3) : null,
                shape: BoxShape.circle,
                boxShadow: _isTyping ? null : WuGrainTheme.lightShadow,
              ),
              child: Icon(
                CupertinoIcons.paperplane,
                color: WuGrainTheme.canvasWhiteColor,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化时间戳
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hr ago';
    } else {
      return '${timestamp.month}/${timestamp.day} ${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }
} 