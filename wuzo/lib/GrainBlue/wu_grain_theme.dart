import 'package:flutter/material.dart';

/// Wuzo木雕主题配置 - 奢华iOS设计系统
class WuGrainTheme {
  // 木雕风格色彩系统
  static const Color pineWoodColor = Color(0xFFE2D5C3);     // 松木色 - 主色调
  static const Color walnutCarveColor = Color(0xFF8A6952);   // 雕刻胡桃木色 - 辅助色调
  static const Color toolSteelColor = Color(0xFF5F646D);     // 工具钢色 - 强调色1
  static const Color charcoalGrayColor = Color(0xFF3C3732);  // 炭灰色 - 强调色2
  static const Color canvasWhiteColor = Color(0xFFFAF8F4);   // 帆布白色 - 文字辅助色
  
  // 功能性色彩
  static const Color successWoodGreen = Color(0xFF67A85A);   // 成功绿
  static const Color warningAmberWood = Color(0xFFB8860B);   // 警告橙
  static const Color errorRedWood = Color(0xFFCD5C5C);       // 错误红
  
  // 渐变色系
  static const List<Color> luxuryGradient = [
    pineWoodColor,
    walnutCarveColor,
    toolSteelColor,
  ];
  
  static const List<Color> shimmerGradient = [
    Color(0xFFFFFFFF),
    Color(0x80FFFFFF),
    Color(0x40FFFFFF),
  ];

  /// 获取主题数据
  static ThemeData get themeData {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: walnutCarveColor,
        brightness: Brightness.light,
        primary: walnutCarveColor,
        secondary: toolSteelColor,
        surface: pineWoodColor,
        onPrimary: canvasWhiteColor,
        onSecondary: canvasWhiteColor,
        onSurface: charcoalGrayColor,
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 32,
          fontWeight: FontWeight.bold,
          color: charcoalGrayColor,
          letterSpacing: -0.5,
        ),
        displayMedium: TextStyle(
          fontSize: 28,
          fontWeight: FontWeight.w600,
          color: charcoalGrayColor,
          letterSpacing: -0.3,
        ),
        headlineLarge: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: toolSteelColor,
          letterSpacing: -0.2,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w500,
          color: toolSteelColor,
        ),
        titleLarge: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w500,
          color: charcoalGrayColor,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
          color: charcoalGrayColor,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: toolSteelColor,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: walnutCarveColor,
          foregroundColor: canvasWhiteColor,
          elevation: 8,
          shadowColor: charcoalGrayColor.withOpacity(0.3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        ),
      ),
      cardTheme: CardTheme(
        color: canvasWhiteColor,
        elevation: 12,
        shadowColor: charcoalGrayColor.withOpacity(0.15),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        margin: const EdgeInsets.all(8),
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.transparent,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: charcoalGrayColor,
        ),
        iconTheme: IconThemeData(
          color: toolSteelColor,
          size: 24,
        ),
      ),
    );
  }

  /// 阴影系统
  static List<BoxShadow> get lightShadow => [
    BoxShadow(
      color: charcoalGrayColor.withOpacity(0.1),
      blurRadius: 8,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: charcoalGrayColor.withOpacity(0.15),
      blurRadius: 12,
      offset: const Offset(0, 4),
    ),
  ];

  static List<BoxShadow> get strongShadow => [
    BoxShadow(
      color: charcoalGrayColor.withOpacity(0.2),
      blurRadius: 16,
      offset: const Offset(0, 6),
    ),
  ];

  /// 奢华渐变装饰
  static BoxDecoration get luxuryGradientDecoration => BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: luxuryGradient,
      stops: const [0.0, 0.6, 1.0],
    ),
  );

  /// 毛玻璃效果装饰
  static BoxDecoration get glassmorphismDecoration => BoxDecoration(
    gradient: LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [
        canvasWhiteColor.withOpacity(0.3),
        canvasWhiteColor.withOpacity(0.1),
      ],
    ),
    borderRadius: BorderRadius.circular(16),
    border: Border.all(
      color: canvasWhiteColor.withOpacity(0.2),
      width: 1,
    ),
    boxShadow: mediumShadow,
  );
} 