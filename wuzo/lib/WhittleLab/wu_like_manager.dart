import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';

/// Wuzo点赞状态管理器 - 统一管理所有帖子的点赞状态
class WuLikeManager {
  static final WuLikeManager _instance = WuLikeManager._internal();
  factory WuLikeManager() => _instance;
  static WuLikeManager get instance => _instance;
  
  WuLikeManager._internal();
  
  SharedPreferences? _prefs;
  
  // 点赞状态存储 - postId: {isLiked: bool, likeCount: int}
  final Map<String, Map<String, dynamic>> _likeStates = {};
  
  // 点赞状态变化流
  final StreamController<Map<String, Map<String, dynamic>>> _likeStreamController = 
      StreamController<Map<String, Map<String, dynamic>>>.broadcast();
  
  /// 点赞状态变化流
  Stream<Map<String, Map<String, dynamic>>> get likeStream => _likeStreamController.stream;
  
  /// 初始化点赞管理器
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _loadLikeStates();
  }
  
  /// 加载点赞状态
  Future<void> _loadLikeStates() async {
    try {
      final String? likeStatesJson = _prefs?.getString('like_states');
      if (likeStatesJson != null) {
        final Map<String, dynamic> decoded = json.decode(likeStatesJson);
        _likeStates.clear();
        decoded.forEach((key, value) {
          _likeStates[key] = Map<String, dynamic>.from(value);
        });
      }
      print('📱 加载点赞状态: ${_likeStates.length} 个帖子');
    } catch (e) {
      print('❌ 加载点赞状态失败: $e');
    }
  }
  
  /// 保存点赞状态
  Future<void> _saveLikeStates() async {
    try {
      final String likeStatesJson = json.encode(_likeStates);
      await _prefs?.setString('like_states', likeStatesJson);
      print('💾 保存点赞状态: ${_likeStates.length} 个帖子');
    } catch (e) {
      print('❌ 保存点赞状态失败: $e');
    }
  }
  
  /// 获取帖子的点赞状态
  Map<String, dynamic> getLikeState(String postId) {
    return _likeStates[postId] ?? {'isLiked': false, 'likeCount': 0};
  }
  
  /// 切换点赞状态
  Future<void> toggleLike(String postId, int originalLikeCount) async {
    final currentState = getLikeState(postId);
    final bool currentIsLiked = currentState['isLiked'] ?? false;
    final int currentLikeCount = currentState['likeCount'] ?? originalLikeCount;
    
    // 切换点赞状态
    final bool newIsLiked = !currentIsLiked;
    final int newLikeCount = newIsLiked 
        ? currentLikeCount + 1 
        : (currentLikeCount > 0 ? currentLikeCount - 1 : 0);
    
    // 更新状态
    _likeStates[postId] = {
      'isLiked': newIsLiked,
      'likeCount': newLikeCount,
    };
    
    // 保存到本地
    await _saveLikeStates();
    
    // 通知监听者
    _likeStreamController.add(Map.from(_likeStates));
    
    print('❤️ 点赞状态更新: $postId, 点赞: $newIsLiked, 点赞数: $newLikeCount');
  }
  
  /// 初始化帖子的点赞状态（如果不存在）
  void initPostLikeState(String postId, bool isLiked, int likeCount) {
    if (!_likeStates.containsKey(postId)) {
      _likeStates[postId] = {
        'isLiked': isLiked,
        'likeCount': likeCount,
      };
    }
  }
  
  /// 获取更新后的帖子对象
  CarveBlueprint getUpdatedPost(CarveBlueprint post) {
    final likeState = getLikeState(post.id);
    return post.copyWith(
      isLiked: likeState['isLiked'] ?? post.isLiked,
      likeCount: likeState['likeCount'] ?? post.likeCount,
    );
  }
  
  /// 清理资源
  void dispose() {
    _likeStreamController.close();
  }
}
