import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Wuzo屏蔽管理器 - 处理帖子屏蔽和举报功能
class WuBlockManager {
  static const String _keyBlockedPosts = 'blocked_posts';
  static const String _keyReportedPosts = 'reported_posts';
  static WuBlockManager? _instance;
  static SharedPreferences? _prefs;
  
  // 屏蔽变化流控制器
  final StreamController<Set<String>> _blockStreamController = 
      StreamController<Set<String>>.broadcast();
  
  Set<String> _blockedPostIds = <String>{};
  Set<String> _reportedPostIds = <String>{};
  
  // 单例模式
  static WuBlockManager get instance {
    _instance ??= WuBlockManager._internal();
    return _instance!;
  }
  
  WuBlockManager._internal();
  
  /// 屏蔽变化流
  Stream<Set<String>> get blockStream => _blockStreamController.stream;
  
  /// 当前屏蔽的帖子ID集合
  Set<String> get blockedPostIds => Set.from(_blockedPostIds);
  
  /// 当前举报的帖子ID集合  
  Set<String> get reportedPostIds => Set.from(_reportedPostIds);
  
  /// 初始化屏蔽管理器
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _loadBlockedPosts();
    await _loadReportedPosts();
    _blockStreamController.add(_blockedPostIds);
  }
  
  /// 屏蔽帖子
  Future<void> blockPost(String postId) async {
    if (postId.isEmpty) return;
    
    _blockedPostIds.add(postId);
    await _saveBlockedPosts();
    _blockStreamController.add(_blockedPostIds);
    print('帖子已屏蔽: $postId, 当前屏蔽数量: ${_blockedPostIds.length}');
  }
  
  /// 解除屏蔽帖子
  Future<void> unblockPost(String postId) async {
    if (postId.isEmpty) return;
    
    final removed = _blockedPostIds.remove(postId);
    if (removed) {
      await _saveBlockedPosts();
      _blockStreamController.add(_blockedPostIds);
      print('帖子已解除屏蔽: $postId, 当前屏蔽数量: ${_blockedPostIds.length}');
    }
  }
  
  /// 举报帖子（举报后自动屏蔽）
  Future<void> reportPost(String postId, String reason) async {
    if (postId.isEmpty) return;
    
    // 添加到举报列表
    _reportedPostIds.add(postId);
    await _saveReportedPosts();
    
    // 自动屏蔽被举报的帖子
    await blockPost(postId);
    
    print('帖子已举报并屏蔽: $postId, 举报原因: $reason');
  }
  
  /// 检查帖子是否被屏蔽
  bool isPostBlocked(String postId) {
    return _blockedPostIds.contains(postId);
  }
  
  /// 检查帖子是否被举报
  bool isPostReported(String postId) {
    return _reportedPostIds.contains(postId);
  }
  
  /// 获取所有屏蔽的帖子ID列表
  List<String> getAllBlockedPostIds() {
    return _blockedPostIds.toList();
  }
  
  /// 清空所有屏蔽
  Future<void> clearAllBlocked() async {
    _blockedPostIds.clear();
    await _saveBlockedPosts();
    _blockStreamController.add(_blockedPostIds);
    print('所有屏蔽已清除');
  }
  
  /// 加载屏蔽帖子列表
  Future<void> _loadBlockedPosts() async {
    try {
      final blockedJson = _prefs?.getString(_keyBlockedPosts);
      if (blockedJson != null) {
        final List<dynamic> blockedList = json.decode(blockedJson);
        _blockedPostIds = blockedList.cast<String>().toSet();
      }
    } catch (e) {
      print('加载屏蔽列表失败: $e');
      _blockedPostIds = <String>{};
    }
  }
  
  /// 加载举报帖子列表
  Future<void> _loadReportedPosts() async {
    try {
      final reportedJson = _prefs?.getString(_keyReportedPosts);
      if (reportedJson != null) {
        final List<dynamic> reportedList = json.decode(reportedJson);
        _reportedPostIds = reportedList.cast<String>().toSet();
      }
    } catch (e) {
      print('加载举报列表失败: $e');
      _reportedPostIds = <String>{};
    }
  }
  
  /// 保存屏蔽帖子列表
  Future<void> _saveBlockedPosts() async {
    try {
      final blockedJson = json.encode(_blockedPostIds.toList());
      await _prefs?.setString(_keyBlockedPosts, blockedJson);
    } catch (e) {
      print('保存屏蔽列表失败: $e');
    }
  }
  
  /// 保存举报帖子列表
  Future<void> _saveReportedPosts() async {
    try {
      final reportedJson = json.encode(_reportedPostIds.toList());
      await _prefs?.setString(_keyReportedPosts, reportedJson);
    } catch (e) {
      print('保存举报列表失败: $e');
    }
  }
  
  /// 销毁管理器
  void dispose() {
    _blockStreamController.close();
  }
} 