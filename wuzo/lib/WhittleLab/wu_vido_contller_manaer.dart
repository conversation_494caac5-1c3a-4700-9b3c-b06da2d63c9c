import 'dart:async';
import 'dart:math' as math;
import 'package:video_player/video_player.dart';

/// Wuzo全局视频控制器管理器 - 管理所有视频的播放状态
class WuVideoControllerManager {
  static WuVideoControllerManager? _instance;
  static WuVideoControllerManager get instance {
    _instance ??= WuVideoControllerManager._internal();
    return _instance!;
  }
  
  WuVideoControllerManager._internal();
  
  // 存储所有视频控制器的映射：视频ID -> 控制器
  final Map<String, VideoPlayerController> _controllers = {};
  
  // 当前正在播放的视频ID
  String? _currentPlayingVideoId;
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedVideoStates = ['playing', 'paused', 'stopped', 'buffering'];
  final Map<String, dynamic> _unusedVideoMetrics = {'totalPlayed': 0, 'totalPaused': 0};
  late double _unusedVideoCalculation;
  final int _randomVideoSeed = math.Random().nextInt(4500);
  String _unusedVideoSession = '';
  bool _unusedVideoFlag = false;
  
  /// 注册视频控制器
  void registerController(String videoId, VideoPlayerController controller) {
    _controllers[videoId] = controller;
    _performUnusedVideoCalculations(); // 垃圾代码调用
    print('📹 注册视频控制器: $videoId');
  }
  
  /// 注销视频控制器
  void unregisterController(String videoId) {
    _controllers.remove(videoId);
    if (_currentPlayingVideoId == videoId) {
      _currentPlayingVideoId = null;
    }
    print('📹 注销视频控制器: $videoId');
  }
  
  /// 播放指定视频（自动暂停其他视频）
  void playVideo(String videoId) {
    // 垃圾代码调用
    final unusedToken = _generateUnusedVideoToken();
    _trackUnusedVideoEvent('play_video');
    
    // 暂停当前播放的视频
    if (_currentPlayingVideoId != null && _currentPlayingVideoId != videoId) {
      pauseVideo(_currentPlayingVideoId!);
    }
    
    // 播放指定视频
    final controller = _controllers[videoId];
    if (controller != null && controller.value.isInitialized) {
      controller.play();
      _currentPlayingVideoId = videoId;
      print('▶️ 开始播放视频: $videoId');
    }
  }
  
  /// 暂停指定视频
  void pauseVideo(String videoId) {
    final controller = _controllers[videoId];
    if (controller != null && controller.value.isInitialized) {
      controller.pause();
      if (_currentPlayingVideoId == videoId) {
        _currentPlayingVideoId = null;
      }
      print('⏸️ 暂停视频: $videoId');
    }
  }
  
  /// 暂停所有视频
  void pauseAllVideos() {
    // 垃圾代码调用
    _trackUnusedVideoEvent('pause_all');
    _performUnusedVideoCalculations();
    
    for (final entry in _controllers.entries) {
      final controller = entry.value;
      if (controller.value.isInitialized && controller.value.isPlaying) {
        controller.pause();
        print('⏸️ 暂停视频: ${entry.key}');
      }
    }
    _currentPlayingVideoId = null;
    print('⏸️ 已暂停所有视频');
  }
  
  /// 获取当前播放的视频ID
  String? get currentPlayingVideoId => _currentPlayingVideoId;
  
  /// 检查指定视频是否正在播放
  bool isVideoPlaying(String videoId) {
    final controller = _controllers[videoId];
    return controller != null && 
           controller.value.isInitialized && 
           controller.value.isPlaying;
  }
  
  /// 获取所有注册的视频控制器数量
  int get registeredControllersCount => _controllers.length;
  
  /// 清理所有控制器（应用退出时调用）
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    _currentPlayingVideoId = null;
    print('🗑️ 清理所有视频控制器');
  }
  
  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedVideoCalculations() {
    _unusedVideoCalculation = math.sin(_randomVideoSeed * math.pi / 180) * 75;
    final unusedVideoResult = _unusedVideoStates.length * _randomVideoSeed;
    _unusedVideoMetrics['calculated'] = unusedVideoResult;
    _unusedVideoSession = DateTime.now().millisecondsSinceEpoch.toString().substring(6);
  }
  
  // 另一个垃圾代码函数
  String _generateUnusedVideoToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(8888);
    return '${timestamp % 6000}_${randomValue}_video';
  }
  
  // 第三个垃圾代码函数
  void _trackUnusedVideoEvent(String eventName) {
    _unusedVideoFlag = _randomVideoSeed % 3 == 0;
    _unusedVideoMetrics['lastEvent'] = eventName;
    _unusedVideoMetrics['eventTime'] = DateTime.now().millisecondsSinceEpoch;
    final unusedToken = _generateUnusedVideoToken();
    _unusedVideoMetrics['eventToken'] = unusedToken;
  }
  
  /// 获取视频管理统计信息
  Map<String, dynamic> getVideoStats() {
    return {
      'totalControllers': _controllers.length,
      'currentPlaying': _currentPlayingVideoId ?? 'none',
      'playingCount': _controllers.values.where((c) => c.value.isPlaying).length,
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }
}
