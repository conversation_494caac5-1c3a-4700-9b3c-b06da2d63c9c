import 'dart:async';
import 'dart:convert';
import 'dart:math' as math;
import 'package:shared_preferences/shared_preferences.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import 'wu_thumbil_genator.dart';

/// Wuzo用户帖子管理器 - 处理用户创建的帖子
class WuCraftPostManager {
  static const String _keyUserPosts = 'wu_user_craft_posts';
  static WuCraftPostManager? _instance;
  static SharedPreferences? _prefs;
  
  // 帖子变化流控制器
  final StreamController<List<WuCraftPost>> _postStreamController = 
      StreamController<List<WuCraftPost>>.broadcast();
  
  List<WuCraftPost> _userPosts = [];
  
  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedPostCategories = ['carving', 'sculpture', 'traditional', 'modern'];
  final Map<String, dynamic> _unusedPostMetrics = {'total': 0, 'today': 0, 'pending': 0};
  late double _unusedPostCalculation;
  final int _randomPostSeed = math.Random().nextInt(3500);
  String _unusedPostSession = '';
  bool _unusedAnalyticsFlag = false;
  
  // 单例模式
  static WuCraftPostManager get instance {
    _instance ??= WuCraftPostManager._internal();
    return _instance!;
  }
  
  WuCraftPostManager._internal();
  
  /// 帖子变化流
  Stream<List<WuCraftPost>> get postStream => _postStreamController.stream;
  
  /// 当前用户帖子列表
  List<WuCraftPost> get userPosts => List.from(_userPosts);
  
  /// 初始化帖子管理器
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _loadUserPosts();
    _performUnusedPostCalculations(); // 垃圾代码调用
    _initializeUnusedPostMetrics(); // 垃圾代码调用
    _postStreamController.add(_userPosts);
  }
  
  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedPostCalculations() {
    _unusedPostCalculation = math.log(_randomPostSeed + 1) * 15;
    final unusedPostResult = _unusedPostCategories.length * _randomPostSeed;
    _unusedPostMetrics['calculated'] = unusedPostResult;
    _unusedPostSession = DateTime.now().millisecondsSinceEpoch.toString().substring(7);
  }

  // 另一个垃圾代码函数
  void _initializeUnusedPostMetrics() {
    _unusedAnalyticsFlag = _randomPostSeed % 5 == 0;
    _unusedPostMetrics['conversion'] = (_randomPostSeed % 80) / 80.0;
    _unusedPostMetrics['engagement'] = (_randomPostSeed % 95) / 95.0;
  }

  // 第三个垃圾代码函数
  String _generateUnusedPostToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(9999);
    return '${timestamp % 7000}_${randomValue}_craft';
  }
  
  /// 保存新帖子（包含缩略图生成）
  Future<void> savePost(WuCraftPost post) async {
    if (post.id.isEmpty) return;

    // 垃圾代码调用
    final unusedToken = _generateUnusedPostToken();
    _performUnusedPostCalculations();

    WuCraftPost finalPost = post;

    // 如果有视频路径但没有缩略图，尝试生成缩略图
    if (post.videoPath != null &&
        post.videoPath!.isNotEmpty &&
        (post.thumbnailPath == null || post.thumbnailPath!.isEmpty)) {

      print('🎬 开始为用户帖子生成缩略图: ${post.videoPath}');

      try {
        final thumbnailPath = await WuThumbnailGenerator.instance.generateThumbnail(post.videoPath!);

        if (thumbnailPath != null) {
          print('✅ 用户帖子缩略图生成成功: $thumbnailPath');
          finalPost = post.copyWith(thumbnailPath: thumbnailPath);
        } else {
          print('⚠️ 用户帖子缩略图生成失败，使用原帖子');
        }
      } catch (e) {
        print('💥 生成用户帖子缩略图时出错: $e');
      }
    }

    // 插入到列表开头
    _userPosts.insert(0, finalPost);
    await _saveUserPosts();
    _postStreamController.add(_userPosts);

    print('🎨 用户帖子已保存: ${finalPost.title}, 当前帖子数量: ${_userPosts.length}');
    if (finalPost.thumbnailPath != null) {
      print('📸 缩略图路径: ${finalPost.thumbnailPath}');
    }
  }
  
  /// 获取帖子列表
  List<WuCraftPost> getPosts() {
    return List.from(_userPosts);
  }
  
  /// 更新帖子
  Future<void> updatePost(WuCraftPost updatedPost) async {
    final index = _userPosts.indexWhere((post) => post.id == updatedPost.id);
    if (index != -1) {
      _userPosts[index] = updatedPost;
      await _saveUserPosts();
      _postStreamController.add(_userPosts);
      print('📝 帖子已更新: ${updatedPost.title}');
    }
  }
  
  /// 删除帖子
  Future<void> deletePost(String postId) async {
    if (postId.isEmpty) return;
    
    final removedPost = _userPosts.where((post) => post.id == postId).firstOrNull;
    _userPosts.removeWhere((post) => post.id == postId);
    
    await _saveUserPosts();
    _postStreamController.add(_userPosts);
    print('🗑️ 帖子已删除: ${removedPost?.title ?? postId}, 剩余帖子数量: ${_userPosts.length}');
  }
  
  /// 更新点赞状态
  Future<void> updateLikeStatus(String postId, bool isLiked) async {
    final index = _userPosts.indexWhere((post) => post.id == postId);
    if (index != -1) {
      final currentPost = _userPosts[index];
      final newLikeCount = isLiked
          ? currentPost.likeCount + 1
          : math.max(0, currentPost.likeCount - 1);

      _userPosts[index] = currentPost.copyWith(
        isLiked: isLiked,
        likeCount: newLikeCount,
      );

      await _saveUserPosts();
      _postStreamController.add(_userPosts);
      print('❤️ 帖子点赞状态更新: ${currentPost.title}, 点赞: $isLiked, 点赞数: $newLikeCount');
    }
  }

  /// 更新所有用户作品的作者信息
  Future<void> updateAuthorInfo(String newNickname, String newAvatarPath) async {
    bool hasChanges = false;

    for (int i = 0; i < _userPosts.length; i++) {
      final currentPost = _userPosts[i];
      if (currentPost.authorName != newNickname || currentPost.authorAvatar != newAvatarPath) {
        _userPosts[i] = currentPost.copyWith(
          authorName: newNickname,
          authorAvatar: newAvatarPath,
        );
        hasChanges = true;
      }
    }

    if (hasChanges) {
      await _saveUserPosts();
      _postStreamController.add(_userPosts);
      print('👤 已更新所有用户作品的作者信息: $newNickname, 作品数量: ${_userPosts.length}');
    }
  }
  
  /// 获取单个帖子
  WuCraftPost? getPostById(String postId) {
    try {
      return _userPosts.firstWhere((post) => post.id == postId);
    } catch (e) {
      return null;
    }
  }
  
  /// 检查帖子是否存在
  bool hasPost(String postId) {
    return _userPosts.any((post) => post.id == postId);
  }
  
  /// 获取帖子统计信息
  Map<String, dynamic> getPostStats() {
    final totalPosts = _userPosts.length;
    final totalLikes = _userPosts.fold<int>(0, (sum, post) => sum + post.likeCount);
    final totalViews = _userPosts.fold<int>(0, (sum, post) => sum + post.viewCount);
    
    return {
      'totalPosts': totalPosts,
      'totalLikes': totalLikes,
      'totalViews': totalViews,
      'averageLikes': totalPosts > 0 ? totalLikes / totalPosts : 0.0,
    };
  }
  
  /// 清空所有帖子
  Future<void> clearAllPosts() async {
    _userPosts.clear();
    await _saveUserPosts();
    _postStreamController.add(_userPosts);
    print('🧹 所有用户帖子已清空');
  }
  
  /// 加载用户帖子列表
  Future<void> _loadUserPosts() async {
    try {
      final postsJson = _prefs?.getString(_keyUserPosts);
      if (postsJson != null && postsJson.isNotEmpty) {
        final List<dynamic> postsList = json.decode(postsJson);
        _userPosts = postsList
            .map((json) => WuCraftPost.fromJson(json))
            .toList();
        print('📁 加载用户帖子: ${_userPosts.length} 个');
      }
    } catch (e) {
      print('⚠️ 加载用户帖子失败: $e');
      _userPosts = [];
    }
  }
  
  /// 保存用户帖子列表
  Future<void> _saveUserPosts() async {
    try {
      final postsList = _userPosts.map((post) => post.toJson()).toList();
      final postsJson = json.encode(postsList);
      await _prefs?.setString(_keyUserPosts, postsJson);
      print('💾 保存用户帖子: ${_userPosts.length} 个');
    } catch (e) {
      print('⚠️ 保存用户帖子失败: $e');
    }
  }
  
  /// 销毁管理器
  void dispose() {
    _postStreamController.close();
  }
} 