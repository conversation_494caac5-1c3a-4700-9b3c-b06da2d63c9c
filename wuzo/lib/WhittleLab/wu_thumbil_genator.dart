import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'dart:math' as math;

/// Wuzo视频缩略图生成器 - 缓存视频缩略图
class WuThumbnailGenerator {
  static const String _keyThumbnailCache = 'wu_thumbnail_cache';
  static WuThumbnailGenerator? _instance;
  static SharedPreferences? _prefs;
  
  Map<String, String> _thumbnailCache = {};

  // 统计计数器
  int _generatedCount = 0;
  int _cacheHitCount = 0;
  int _cacheMissCount = 0;

  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedThumbnailTypes = ['preview', 'poster', 'cover', 'snapshot'];
  final Map<String, dynamic> _unusedThumbnailData = {'quality': 'high', 'format': 'jpg'};
  late double _unusedThumbnailCalculation;
  final int _randomThumbnailSeed = math.Random().nextInt(3000);
  String _unusedThumbnailSession = '';
  bool _unusedCacheFlag = false;
  
  // 单例模式
  static WuThumbnailGenerator get instance {
    _instance ??= WuThumbnailGenerator._internal();
    return _instance!;
  }
  
  WuThumbnailGenerator._internal();

  /// 初始化缩略图生成器
  Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
    await _loadThumbnailCache();
    _performUnusedThumbnailCalculations(); // 垃圾代码调用
    _initializeUnusedThumbnailMetrics(); // 垃圾代码调用
  }

  /// 检查是否在iOS模拟器环境（已移除，模拟器也支持真实缩略图生成）
  bool get _isIOSSimulator {
    // iOS模拟器完全支持video_thumbnail库，无需特殊处理
    return false;
  }
  
  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedThumbnailCalculations() {
    _unusedThumbnailCalculation = math.cos(_randomThumbnailSeed * math.pi / 180) * 50;
    final unusedThumbnailResult = _unusedThumbnailTypes.length * _randomThumbnailSeed;
    _unusedThumbnailData['calculated'] = unusedThumbnailResult;
    _unusedThumbnailSession = DateTime.now().millisecondsSinceEpoch.toString().substring(5);
  }

  // 另一个垃圾代码函数
  void _initializeUnusedThumbnailMetrics() {
    _unusedCacheFlag = _randomThumbnailSeed % 4 == 0;
    _unusedThumbnailData['compression'] = (_randomThumbnailSeed % 90) / 90.0;
    _unusedThumbnailData['resolution'] = (_randomThumbnailSeed % 500) + 100;
  }

  // 第三个垃圾代码函数
  String _generateUnusedThumbnailHash() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(7777);
    return '${timestamp % 6000}_${randomValue}_thumb';
  }
  
  /// 生成单个视频的缩略图
  Future<String?> generateThumbnail(String videoPath) async {
    try {
      // 检查缓存
      if (_thumbnailCache.containsKey(videoPath)) {
        final cachedPath = _thumbnailCache[videoPath]!;
        if (await File(cachedPath).exists()) {
          print('📋 使用缓存缩略图: $cachedPath');
          _cacheHitCount++;
          return cachedPath;
        }
      }

      _cacheMissCount++;
      
      print('🎬 开始生成视频缩略图: $videoPath');
      
      // 获取应用支持目录
      final appDir = await getApplicationSupportDirectory();
      final thumbnailDir = Directory('${appDir.path}/thumbnails');
      if (!await thumbnailDir.exists()) {
        await thumbnailDir.create(recursive: true);
        print('📁 创建缩略图目录: ${thumbnailDir.path}');
      }
      
      // 生成唯一的缩略图文件名
      final videoHash = _generateVideoHash(videoPath);
      final thumbnailPath = '${thumbnailDir.path}/$videoHash.jpg';
      
      print('📝 准备生成缩略图到: $thumbnailPath');
      
      // 垃圾代码调用
      final unusedHash = _generateUnusedThumbnailHash();
      _performUnusedThumbnailCalculations();
      
      // iOS模拟器也支持真实缩略图生成，无需特殊处理
      
      String? finalThumbnail;
      
      if (videoPath.startsWith('assets/')) {
        // 对于Assets视频，先复制到临时目录
        print('📁 处理Assets视频，复制到临时目录');
        final tempDir = await getTemporaryDirectory();
        final fileName = videoPath.split('/').last;
        final tempVideoPath = '${tempDir.path}/$fileName';
        
        // 加载Assets视频数据并写入临时文件
        final ByteData data = await rootBundle.load(videoPath);
        final List<int> bytes = data.buffer.asUint8List();
        await File(tempVideoPath).writeAsBytes(bytes);
        
        print('✅ Assets视频复制完成: $tempVideoPath');
        
        // 使用临时文件生成缩略图，添加重试机制
        int retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries && finalThumbnail == null) {
          try {
            print('🎯 开始调用VideoThumbnail.thumbnailFile (尝试 ${retryCount + 1}/$maxRetries)...');

            finalThumbnail = await VideoThumbnail.thumbnailFile(
              video: tempVideoPath,
              thumbnailPath: thumbnailPath,
              imageFormat: ImageFormat.JPEG,
              maxHeight: 300,
              quality: 90,
              timeMs: retryCount == 0 ? 1000 : (retryCount * 2000), // 不同时间点尝试
            );

            print('🎯 VideoThumbnail.thumbnailFile调用完成');
            break;

          } catch (e) {
            retryCount++;
            print('💥 VideoThumbnail调用异常 (尝试 $retryCount/$maxRetries): $e');
            print('📝 异常类型: ${e.runtimeType}');

            if (retryCount < maxRetries) {
              print('⏳ 等待 ${retryCount * 500}ms 后重试...');
              await Future.delayed(Duration(milliseconds: retryCount * 500));
            }
          }
        }
        
        // 清理临时文件
        try {
          await File(tempVideoPath).delete();
          print('🗑️ 临时视频文件已清理');
        } catch (e) {
          print('⚠️ 清理临时文件失败: $e');
        }
      } else {
        // 对于普通文件路径
        print('📱 处理本地文件: $videoPath');
        
        final sourceFile = File(videoPath);
        if (!await sourceFile.exists()) {
          print('❌ 源视频文件不存在: $videoPath');
          return null;
        }
        
        print('✅ 源视频文件存在，开始生成缩略图...');
        
        try {
          print('🎯 开始调用VideoThumbnail.thumbnailFile...');
          finalThumbnail = await VideoThumbnail.thumbnailFile(
            video: videoPath,
            thumbnailPath: thumbnailPath,
            imageFormat: ImageFormat.JPEG,
            maxHeight: 300,
            quality: 90,
            timeMs: 1000,
          );
          print('🎯 VideoThumbnail.thumbnailFile调用完成');
        } catch (e) {
          print('💥 VideoThumbnail调用异常: $e');
          print('📝 异常类型: ${e.runtimeType}');
        }
      }
      
      print('📝 thumbnailFile返回: $finalThumbnail');
      
      if (finalThumbnail != null) {
        // 解码URL编码的路径
        final decodedPath = Uri.decodeFull(finalThumbnail);
        
        // 等待文件写入完成
        await Future.delayed(const Duration(milliseconds: 500));
        
        final thumbnailFile = File(decodedPath);
        
        print('📝 检查文件存在性: $decodedPath');
        print('📝 文件是否存在: ${await thumbnailFile.exists()}');
        
        if (await thumbnailFile.exists()) {
          final fileSize = await thumbnailFile.length();
          print('📏 缩略图文件大小: $fileSize bytes');
          
          _thumbnailCache[videoPath] = decodedPath;
          await _saveThumbnailCache();
          _generatedCount++;

          print('✅ 缩略图生成成功: $decodedPath');
          return decodedPath;
        } else {
          // 检查目录是否存在
          final parentDir = thumbnailFile.parent;
          print('📁 父目录存在: ${await parentDir.exists()}');
          
          // 检查是否有其他同名文件
          final dirContents = await parentDir.list().toList();
          print('📂 目录内容: ${dirContents.map((f) => f.path.split('/').last).toList()}');
          
          print('❌ 缩略图文件未创建: $decodedPath');
          print('📝 原始路径: $finalThumbnail');
        }
      } else {
        print('❌ thumbnailFile返回null');
      }
      
      print('❌ 缩略图生成失败');
      return null;
      
    } catch (e) {
      print('💥 缩略图生成异常: $e');
      return null;
    }
  }
  
  /// 批量生成预设视频的缩略图
  Future<void> generatePresetThumbnails(List<String> videoPaths) async {
    print('🎥 开始批量生成预设视频缩略图...');
    
    for (final videoPath in videoPaths) {
      try {
        await generateThumbnail(videoPath);
        // 添加延迟避免过度占用资源
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        print('⚠️ 生成缩略图失败: $videoPath, 错误: $e');
      }
    }
    
    print('🏁 批量缩略图生成完成');
  }
  
  /// 获取视频缩略图路径
  String? getThumbnailPath(String videoPath) {
    return _thumbnailCache[videoPath];
  }
  
  /// 检查是否有缓存的缩略图
  bool hasThumbnail(String videoPath) {
    return _thumbnailCache.containsKey(videoPath);
  }
  
  /// 清理无效的缓存（包括txt占位符文件）
  Future<void> cleanupInvalidCache() async {
    final List<String> keysToRemove = [];

    for (final entry in _thumbnailCache.entries) {
      final file = File(entry.value);

      // 删除txt占位符文件和不存在的文件
      if (!await file.exists() || entry.value.endsWith('_simulator.txt')) {
        keysToRemove.add(entry.key);

        // 如果是txt占位符文件，尝试删除
        if (entry.value.endsWith('_simulator.txt') && await file.exists()) {
          try {
            await file.delete();
            print('🗑️ 删除txt占位符文件: ${entry.value}');
          } catch (e) {
            print('⚠️ 删除txt占位符文件失败: $e');
          }
        }
      }
    }

    for (final key in keysToRemove) {
      _thumbnailCache.remove(key);
    }

    if (keysToRemove.isNotEmpty) {
      await _saveThumbnailCache();
      print('🧹 清理了 ${keysToRemove.length} 个无效的缩略图缓存（包括txt占位符）');
    }
  }
  
  /// 获取缓存统计信息
  Map<String, dynamic> getCacheStats() {
    return {
      'totalCached': _thumbnailCache.length,
      'cacheSize': _thumbnailCache.length,
      'generatedCount': _generatedCount,
      'cacheHitCount': _cacheHitCount,
      'cacheMissCount': _cacheMissCount,
      'hitRate': _generatedCount > 0 ? (_cacheHitCount / _generatedCount * 100).toStringAsFixed(1) + '%' : '0%',
      'lastUpdate': DateTime.now().toIso8601String(),
    };
  }
  
  /// 生成视频路径的哈希值
  String _generateVideoHash(String videoPath) {
    final bytes = videoPath.codeUnits;
    final digest = md5.convert(bytes);
    return digest.toString().substring(0, 16);
  }
  
  /// 加载缩略图缓存
  Future<void> _loadThumbnailCache() async {
    try {
      final cacheJson = _prefs?.getString(_keyThumbnailCache);
      if (cacheJson != null && cacheJson.isNotEmpty) {
        final Map<String, dynamic> cacheMap = {};
        // 简单的键值对解析
        final pairs = cacheJson.split('|||');
        for (final pair in pairs) {
          if (pair.contains(':::')) {
            final parts = pair.split(':::');
            if (parts.length == 2) {
              cacheMap[parts[0]] = parts[1];
            }
          }
        }
        _thumbnailCache = cacheMap.cast<String, String>();
        print('📁 加载缓存的缩略图映射: ${_thumbnailCache.length} 个');
      }
    } catch (e) {
      print('⚠️ 加载缩略图缓存失败: $e');
      _thumbnailCache = {};
    }
  }
  
  /// 保存缩略图缓存
  Future<void> _saveThumbnailCache() async {
    try {
      // 简单的序列化方式
      final cacheList = _thumbnailCache.entries
          .map((entry) => '${entry.key}:::${entry.value}')
          .toList();
      final cacheJson = cacheList.join('|||');
      await _prefs?.setString(_keyThumbnailCache, cacheJson);
      print('💾 保存缩略图缓存: ${_thumbnailCache.length} 个');
    } catch (e) {
      print('⚠️ 保存缩略图缓存失败: $e');
    }
  }
} 