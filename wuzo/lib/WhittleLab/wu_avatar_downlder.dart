import 'dart:io';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';

/// Wuzo头像下载服务 - 使用Unsplash API
class WuAvatarDownloader {
  static const String _accessKey = 'sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM';
  static const String _baseUrl = 'https://api.unsplash.com';
  
  final Dio _dio = Dio();
  
  WuAvatarDownloader() {
    _dio.options.headers['Authorization'] = 'Client-ID $_accessKey';
  }

  /// 下载头像图片到本地
  Future<String> downloadAvatar({
    required String fileName,
    String query = 'portrait face person',
    int width = 200,
    int height = 200,
  }) async {
    try {
      // 检查本地是否已存在
      final localPath = await getLocalAvatarPath(fileName);
      final file = File(localPath);
      
      if (await file.exists()) {
        print('头像已存在本地: $fileName');
        return localPath;
      }

      print('开始下载头像: $fileName');
      
      // 从Unsplash获取随机头像
      final response = await _dio.get(
        '$_baseUrl/photos/random',
        queryParameters: {
          'query': query,
          'w': width,
          'h': height,
          'fit': 'crop',
          'crop': 'face',
        },
      );

      if (response.statusCode == 200) {
        final imageUrl = response.data['urls']['regular'] as String;
        
        // 下载图片数据
        final imageResponse = await _dio.get(
          imageUrl,
          options: Options(responseType: ResponseType.bytes),
        );
        
        if (imageResponse.statusCode == 200) {
          // 保存到本地
          await file.writeAsBytes(imageResponse.data as Uint8List);
          print('头像下载成功: $fileName');
          return localPath;
        }
      }
      
      throw Exception('Failed to download avatar: ${response.statusCode}');
    } catch (e) {
      print('头像下载失败: $fileName, 错误: $e');
      // 返回默认头像路径
      return await _getDefaultAvatarPath();
    }
  }

  /// 批量下载多个头像
  Future<List<String>> downloadMultipleAvatars({
    required List<String> fileNames,
    String query = 'portrait face person',
  }) async {
    final List<String> results = [];
    
    for (int i = 0; i < fileNames.length; i++) {
      final fileName = fileNames[i];
      
      // 为每个头像使用不同的查询词以增加多样性
      final queries = [
        'portrait face person man',
        'portrait face person woman',
        'portrait face artist',
        'portrait face craftsman',
        'portrait face designer',
        'portrait face maker',
        'portrait face teacher',
        'portrait face creator',
      ];
      
      final currentQuery = queries[i % queries.length];
      
      try {
        final path = await downloadAvatar(
          fileName: fileName,
          query: currentQuery,
        );
        results.add(path);
        
        // 延迟一下，避免API限制
        await Future.delayed(const Duration(milliseconds: 500));
      } catch (e) {
        print('批量下载头像失败: $fileName, 错误: $e');
        results.add(await _getDefaultAvatarPath());
      }
    }
    
    return results;
  }

  /// 获取本地头像文件路径
  Future<String> getLocalAvatarPath(String fileName) async {
    final appDir = await getApplicationDocumentsDirectory();
    final avatarDir = Directory('${appDir.path}/avatars');
    
    if (!await avatarDir.exists()) {
      await avatarDir.create(recursive: true);
    }
    
    return '${avatarDir.path}/$fileName.jpg';
  }

  /// 获取默认头像路径
  Future<String> _getDefaultAvatarPath() async {
    // 返回assets中的默认头像，如果需要的话
    return 'assets/images/default_avatar.png';
  }

  /// 检查头像是否已下载
  Future<bool> isAvatarDownloaded(String fileName) async {
    final localPath = await getLocalAvatarPath(fileName);
    return await File(localPath).exists();
  }

  /// 清理所有下载的头像
  Future<void> clearAllAvatars() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final avatarDir = Directory('${appDir.path}/avatars');
      
      if (await avatarDir.exists()) {
        await avatarDir.delete(recursive: true);
        print('所有头像已清理');
      }
    } catch (e) {
      print('清理头像失败: $e');
    }
  }
} 