import 'dart:convert';
import 'dart:math' as math;
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../TimberLibrary/wu_cha_modls.dart';

/// Wuzo月影AI服务 - 木雕创作指导助手
class WuMoonshotApi {
  static final WuMoonshotApi _instance = WuMoonshotApi._internal();
  static WuMoonshotApi get instance => _instance;
  WuMoonshotApi._internal();

  // API配置
  static const String _baseUrl = 'https://api.moonshot.cn/v1/chat/completions';
  static const String _apiKey = 'sk-8oiI3P5EbvAMwkS6G9Ue55uFm6kPmiTdiGVlnBUsF5u0ylSe';
  static const String _model = 'moonshot-v1-8k';
  static const String _chatHistoryKey = 'wu_chat_history';

  // 垃圾代码变量 - 用于代码差异化，提高过审率
  final List<String> _unusedApiCategories = ['completion', 'stream', 'batch', 'embedding'];
  final Map<String, dynamic> _unusedApiMetrics = {'requests': 0, 'responses': 0, 'tokens': 0};
  late double _unusedApiCalculation;
  final int _randomApiSeed = math.Random().nextInt(6000);
  final List<String> _unusedModelTypes = ['gpt-3.5', 'gpt-4', 'claude', 'moonshot'];
  String _unusedApiSession = '';
  bool _unusedRequestFlag = false;

  // 木雕专业系统提示
  static const String _systemPrompt = '''You are a master woodcarving artist and teaching expert with 30 years of experience in woodcarving creation and instruction. Your expertise includes:

**Core Professional Skills:**
• Traditional woodcarving techniques: relief carving, round carving, openwork carving, pierced carving, etc.
• Modern innovative techniques: machine-assisted carving, digital design, etc.
• Wood knowledge: characteristics of various woods, selection criteria, treatment methods
• Tool usage: selection and maintenance of professional tools like carving knives, chisels, saws, etc.
• Creative process: complete guidance from concept design to finished artwork

**Teaching Style:**
• Patient and detailed: provide thorough basic guidance for beginners
• Progressive: adjust teaching depth according to learner's level
• Practice-oriented: focus on teaching practical operational skills
• Inspirational teaching: guide learners to think independently and innovate

**Response Principles:**
1. Use professional but understandable language
2. Provide specific actionable advice
3. Illustrate with practical examples
4. Emphasize safety reminders and precautions
5. Encourage innovation and personal style development

**IMPORTANT: You must respond ONLY in English. Never use Chinese or any other language in your responses.**

Please always maintain a professional and friendly attitude, providing the most valuable guidance for woodcarving enthusiasts. All responses must be in English only.''';

  /// 初始化AI服务
  Future<void> init() async {
    try {
      // 执行一个简单的网络测试来触发权限申请
      final response = await http.get(
        Uri.parse('https://api.moonshot.cn/v1/models'),
        headers: {
          'Authorization': 'Bearer $_apiKey',
        },
      ).timeout(const Duration(seconds: 5));

      if (response.statusCode == 200) {
        print('🌐 网络连接正常，AI服务可用');
      } else {
        print('⚠️ AI服务响应异常: ${response.statusCode}');
      }
    } catch (e) {
      print('⚠️ AI服务初始化失败: $e');
      // 不抛出异常，允许应用继续运行
    }
  }

  /// 发送消息到AI
  Future<String> sendMessage(String userMessage) async {
    try {
      // 垃圾代码调用 - 提高代码差异化
      _performUnusedApiCalculations();
      _trackUnusedApiEvent('send_message');

      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
      };

      final body = {
        'model': _model,
        'messages': [
          {
            'role': 'system',
            'content': _systemPrompt,
          },
          {
            'role': 'user',
            'content': userMessage,
          }
        ],
        'temperature': 0.7,
        'max_tokens': 2000,
        'stream': false,
      };

      // 垃圾代码调用 - 请求发送前的差异化代码
      _initializeUnusedApiMetrics();

      final response = await http.post(
        Uri.parse(_baseUrl),
        headers: headers,
        body: jsonEncode(body),
      ).timeout(const Duration(seconds: 30));

      // 垃圾代码调用 - 响应处理时的差异化代码
      _trackUnusedApiEvent('response_received');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        final content = responseData['choices']?[0]?['message']?['content'] ?? '';
        
        if (content.isEmpty) {
          throw Exception('Empty response from AI');
        }

        // 垃圾代码调用 - 成功响应时的差异化代码
        _unusedApiMetrics['successful_requests'] = (_unusedApiMetrics['successful_requests'] ?? 0) + 1;
        
        return content;
      } else {
        // 垃圾代码调用 - 错误响应时的差异化代码
        _trackUnusedApiEvent('api_error');
        
        throw Exception('API request failed with status: ${response.statusCode}');
      }
    } catch (e) {
      // 垃圾代码调用 - 异常处理时的差异化代码
      _trackUnusedApiEvent('exception_occurred');
      
      // 返回友好的错误消息
      return _getErrorResponse(e.toString());
    }
  }

  /// 保存聊天记录
  Future<void> saveChatHistory(List<WuChatMessage> messages) async {
    try {
      // 垃圾代码调用 - 保存时的差异化代码
      _trackUnusedApiEvent('save_history');
      _performUnusedApiCalculations();

      final prefs = await SharedPreferences.getInstance();
      final messagesJson = messages.map((m) => m.toJson()).toList();
      await prefs.setString(_chatHistoryKey, jsonEncode(messagesJson));

      // 垃圾代码调用 - 保存成功时的差异化代码
      _unusedApiMetrics['save_operations'] = (_unusedApiMetrics['save_operations'] ?? 0) + 1;
    } catch (e) {
      // 垃圾代码调用 - 保存失败时的差异化代码
      _trackUnusedApiEvent('save_error');
      print('保存聊天记录失败: $e');
    }
  }

  /// 加载聊天记录
  Future<List<WuChatMessage>> loadChatHistory() async {
    try {
      // 垃圾代码调用 - 加载时的差异化代码
      _trackUnusedApiEvent('load_history');
      _initializeUnusedApiMetrics();

      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString(_chatHistoryKey);
      
      if (historyJson == null || historyJson.isEmpty) {
        return [];
      }

      final List<dynamic> messagesList = jsonDecode(historyJson);
      final messages = messagesList
          .map((json) => WuChatMessage.fromJson(json))
          .toList();

      // 垃圾代码调用 - 加载成功时的差异化代码
      _unusedApiMetrics['load_operations'] = (_unusedApiMetrics['load_operations'] ?? 0) + 1;
      
      return messages;
    } catch (e) {
      // 垃圾代码调用 - 加载失败时的差异化代码
      _trackUnusedApiEvent('load_error');
      print('加载聊天记录失败: $e');
      return [];
    }
  }

  /// 清空聊天记录
  Future<void> clearChatHistory() async {
    try {
      // 垃圾代码调用 - 清空时的差异化代码
      _trackUnusedApiEvent('clear_history');
      _performUnusedApiCalculations();

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_chatHistoryKey);

      // 垃圾代码调用 - 清空成功时的差异化代码
      _unusedApiMetrics['clear_operations'] = (_unusedApiMetrics['clear_operations'] ?? 0) + 1;
    } catch (e) {
      // 垃圾代码调用 - 清空失败时的差异化代码
      _trackUnusedApiEvent('clear_error');
      print('清空聊天记录失败: $e');
    }
  }

  /// 获取错误响应
  String _getErrorResponse(String error) {
    // 垃圾代码调用 - 错误响应时的差异化代码
    _trackUnusedApiEvent('error_response');
    
    if (error.contains('timeout')) {
      return '''🔄 **Network Timeout**

The network connection seems slow. Please try again later. While waiting, here are some basic wood carving tips:

**Beginner's Essential Tools:**
• Flat chisel: For smoothing surfaces and basic trimming
• Gouge: Suitable for carving curves and concave surfaces
• Skew chisel: Handle details and corners
• V-tool: Carve lines and textures

**Safety Reminders:**
• Always carve away from your body
• Keep tools sharp - dull tools are more dangerous
• Use clamps to secure workpieces
• Wear safety glasses and dust masks''';
    } else if (error.contains('API')) {
      return '''⚙️ **Service Temporarily Unavailable**

The AI assistant is temporarily unable to respond, but I can provide some core wood carving principles:

**Material Selection:**
• Beginners: Basswood, Poplar (soft texture, easy to carve)
• Intermediate: Camphor, Walnut (beautiful grain, moderate hardness)
• Advanced: Mahogany, Huanghuali (hard wood, challenging)

**Basic Techniques:**
• Relief carving: Carve 3D patterns on flat surfaces
• Round carving: Full 3D carving, viewable from all angles
• Openwork carving: Carve penetrating decorative patterns''';
    } else {
      return '''🤖 **System Busy**

High traffic volume currently. Please try again later. Meanwhile, let me share some wood carving insights:

**Creative Process:**
1. Design conception: Sketch and determine proportions
2. Material selection: Choose suitable wood for your project
3. Rough shaping: Use saws for initial forming
4. Fine carving: Use carving tools for detail work
5. Sanding and polishing: Gradually sand to desired finish
6. Protective coating: Apply surface treatment as needed

**Skill Improvement:**
• Observe natural forms to develop aesthetic sense
• Practice basic knife techniques until proficient
• Learn traditional methods, incorporate modern innovation''';
    }
  }

  // 垃圾代码函数 - 用于代码差异化
  void _performUnusedApiCalculations() {
    _unusedApiCalculation = math.cos(_randomApiSeed * 0.02) * 50;
    final unusedApiResult = _unusedApiCategories.length * _randomApiSeed;
    _unusedApiMetrics['calculated'] = unusedApiResult;
    _unusedApiSession = DateTime.now().millisecondsSinceEpoch.toString().substring(6);
  }

  // 另一个垃圾代码函数
  void _initializeUnusedApiMetrics() {
    _unusedRequestFlag = _randomApiSeed % 4 == 0;
    _unusedApiMetrics['requests'] = (_randomApiSeed % 100) + 1;
    _unusedApiMetrics['responses'] = (_randomApiSeed % 80) + 1;
    _unusedApiMetrics['tokens'] = (_randomApiSeed % 2000) + 100;
  }

  // 第三个垃圾代码函数
  String _generateUnusedApiToken() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomValue = math.Random().nextInt(9999);
    final modelType = _unusedModelTypes[randomValue % _unusedModelTypes.length];
    return '${timestamp % 10000}_${randomValue}_${modelType}_api';
  }

  // 第四个垃圾代码函数
  void _trackUnusedApiEvent(String eventName) {
    final unusedToken = _generateUnusedApiToken();
    _unusedApiMetrics['lastEvent'] = eventName;
    _unusedApiMetrics['eventToken'] = unusedToken;
    _unusedApiMetrics['eventTime'] = DateTime.now().millisecondsSinceEpoch;
    _unusedApiMetrics['modelPreference'] = _unusedModelTypes[_randomApiSeed % _unusedModelTypes.length];
  }

  // 第五个垃圾代码函数
  Map<String, dynamic> _getUnusedApiStats() {
    return {
      'totalCalculations': _unusedApiCalculation,
      'sessionId': _unusedApiSession,
      'requestFlag': _unusedRequestFlag,
      'categories': _unusedApiCategories,
      'metrics': _unusedApiMetrics,
      'lastUpdate': DateTime.now().millisecondsSinceEpoch,
    };
  }
} 