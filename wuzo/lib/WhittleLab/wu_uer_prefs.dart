import 'package:shared_preferences/shared_preferences.dart';

/// Wuzo用户偏好设置管理器
class WuUserPrefs {
  static const String _keyUserNickname = 'user_nickname';
  static const String _keyUserAvatar = 'user_avatar';
  
  static SharedPreferences? _prefs;
  
  /// 初始化SharedPreferences
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }
  
  /// 保存用户昵称
  static Future<bool> saveUserNickname(String nickname) async {
    await init();
    return await _prefs!.setString(_keyUserNickname, nickname);
  }
  
  /// 获取用户昵称
  static Future<String> getUserNickname() async {
    await init();
    return _prefs!.getString(_keyUserNickname) ?? 'Timber Artisan';
  }
  
  /// 保存用户头像路径
  static Future<bool> saveUserAvatar(String avatarPath) async {
    await init();
    return await _prefs!.setString(_keyUserAvatar, avatarPath);
  }
  
  /// 获取用户头像路径
  static Future<String> getUserAvatar() async {
    await init();
    return _prefs!.getString(_keyUserAvatar) ?? 'assets/avatars/avatar_lisa.jpg';
  }
  
  /// 清除所有用户数据
  static Future<bool> clearUserData() async {
    await init();
    await _prefs!.remove(_keyUserNickname);
    await _prefs!.remove(_keyUserAvatar);
    return true;
  }
} 