import '../TimberLibrary/wu_craft_blueprint.dart';

/// Wuzo模拟数据服务 - 预设的木雕作品和用户数据
class WuMockData {
  /// 预设工匠用户数据
  static List<WuArtisan> get mockArtisans => [
    const WuArtisan(
      id: 'artisan_001',
      nickname: 'WoodMaster Chen',
      avatarUrl: 'assets/avatars/avatar_chen.jpg', // 本地assets路径
      bio: 'Traditional wood carving artist with 15+ years experience',
      followerCount: 1250,
      followingCount: 89,
      carveLevel: 1,
      specialties: ['Relief Carving', 'Figure Carving'],
      isVerified: true,
    ),
    const WuArtisan(
      id: 'artisan_002',
      nickname: 'CraftGuru Alex',
      avatarUrl: 'assets/avatars/avatar_alex.jpg',
      bio: 'Modern woodworking techniques meets traditional artistry',
      followerCount: 876,
      followingCount: 156,
      carveLevel: 7,
      specialties: ['Abstract Art', 'Modern Design'],
      isVerified: false,
    ),
    const <PERSON><PERSON><PERSON><PERSON>(
      id: 'artisan_003',
      nickname: 'Carve<PERSON><PERSON><PERSON>',
      avatarUrl: 'assets/avatars/avatar_sarah.jpg',
      bio: 'Passionate about intricate detail work and miniatures',
      followerCount: 2340,
      followingCount: 234,
      carveLevel: 8,
      specialties: ['Miniature Carving', 'Detail Work'],
      isVerified: true,
    ),
    const WuArtisan(
      id: 'artisan_004',
      nickname: 'TimberArt Mike',
      avatarUrl: 'assets/avatars/avatar_mike.jpg',
      bio: 'Teaching traditional techniques to new generation',
      followerCount: 567,
      followingCount: 78,
      carveLevel: 6,
      specialties: ['Teaching', 'Traditional Methods'],
      isVerified: false,
    ),
    const WuArtisan(
      id: 'artisan_005',
      nickname: 'WoodSpirit Emma',
      avatarUrl: 'assets/avatars/avatar_emma.jpg',
      bio: 'Bringing nature to life through wood carving',
      followerCount: 1890,
      followingCount: 312,
      carveLevel: 8,
      specialties: ['Nature Art', 'Animal Carving'],
      isVerified: true,
    ),
    const WuArtisan(
      id: 'artisan_006',
      nickname: 'CraftLegend Tom',
      avatarUrl: 'assets/avatars/avatar_tom.jpg',
      bio: 'Master craftsman sharing ancient woodworking secrets',
      followerCount: 3456,
      followingCount: 45,
      carveLevel: 10,
      specialties: ['Ancient Techniques', 'Tool Making'],
      isVerified: true,
    ),
    const WuArtisan(
      id: 'artisan_007',
      nickname: 'ArtisanRookie Jin',
      avatarUrl: 'assets/avatars/avatar_jin.jpg',
      bio: 'New to carving but passionate about learning',
      followerCount: 234,
      followingCount: 567,
      carveLevel: 3,
      specialties: ['Learning', 'Basic Techniques'],
      isVerified: false,
    ),
    const WuArtisan(
      id: 'artisan_008',
      nickname: 'DetailMaster Lisa',
      avatarUrl: 'assets/avatars/avatar_lisa.jpg',
      bio: 'Precision and perfection in every carved line',
      followerCount: 1678,
      followingCount: 123,
      carveLevel: 9,
      specialties: ['Precision Work', 'Fine Details'],
      isVerified: true,
    ),
  ];

  /// 预设木雕作品数据
  static List<CarveBlueprint> get mockCarvingPosts => [
    CarveBlueprint(
      id: 'post_001',
      title: 'Wooden handicraft art',
      description: 'The charm and fun of unique handmade art.',
      videoUrl: 'assets/av/wood1.mp4',
      thumbnailUrl: 'assets/images/mudiao1_thumb.jpg',
      creator: mockArtisans[0],
      publishTime: DateTime.now().subtract(const Duration(hours: 2)),
      likeCount: 342,
      viewCount: 1567,
      isLiked: false,
      tags: ['art', 'Wooden',],
      difficulty: 'Hard',
      videoDuration: const Duration(seconds: 45),
    ),
    CarveBlueprint(
      id: 'post_002',
      title: 'This is so impressive!!',
      description: 'I carved the wood into an ocean,which will show different luster in different light.',
      videoUrl: 'assets/av/wood2.mp4',
      thumbnailUrl: 'assets/images/mudiao2_thumb.jpg',
      creator: mockArtisans[1],
      publishTime: DateTime.now().subtract(const Duration(hours: 6)),
      likeCount: 189,
      viewCount: 892,
      isLiked: true,
      tags: ['woodworking', 'ocean', 'texture', 'handmade'],
      difficulty: 'Medium',
      videoDuration: const Duration(seconds: 38),
    ),
    CarveBlueprint(
      id: 'post_003',
      title: 'Hand-Carved Wood Art for Modern Spaces',
      description: 'Transform your space with this stunning abstract walnut wood sculpture! 🌿 Hand-carved from organic wood, every curve and grain tells a story of nature and artistry. Finished with silky-smooth Osmo wax, it’s a one-of-a-kind piece that adds warmth and elegance to any interior.',
      videoUrl: 'assets/av/wood3.mp4',
      thumbnailUrl: 'assets/images/mudiao3_thumb.jpg',
      creator: mockArtisans[2],
      publishTime: DateTime.now().subtract(const Duration(hours: 12)),
      likeCount: 567,
      viewCount: 2134,
      isLiked: false,
      tags: ['WoodSculpture', 'AbstractArt', 'HandmadeWithLove', 'WalnutWood'],
      difficulty: 'Hard',
      videoDuration: const Duration(seconds: 52),
    ),
    CarveBlueprint(
      id: 'post_004',
      title: 'Handcrafted wood sculptures',
      description: 'The magic of fingertips, giving life to wood, handmade art.',
      videoUrl: 'assets/av/wood4.mp4',
      thumbnailUrl: 'assets/images/mudiao1_thumb.jpg',
      creator: mockArtisans[3],
      publishTime: DateTime.now().subtract(const Duration(days: 1)),
      likeCount: 234,
      viewCount: 756,
      isLiked: true,
      tags: ['woodcarving', 'wooden', 'wood working', 'sculptureart'],
      difficulty: 'Easy',
      videoDuration: const Duration(seconds: 41),
    ),
    CarveBlueprint(
      id: 'post_005',
      title: 'Father carving oak panel for church iconostasis!',
      description: 'Hard and time-consuming wood to choose but the result is amazing!!!!',
      videoUrl: 'assets/av/wood5.mp4',
      thumbnailUrl: 'assets/images/mudiao2_thumb.jpg',
      creator: mockArtisans[4],
      publishTime: DateTime.now().subtract(const Duration(days: 2)),
      likeCount: 445,
      viewCount: 1823,
      isLiked: false,
      tags: ['carving_by_hand', 'carvingvideo', 'how to carve video'],
      difficulty: 'Hard',
      videoDuration: const Duration(seconds: 48),
    ),
    CarveBlueprint(
      id: 'post_006',
      title: 'lioncarving',
      description: 'Ahmad Alhambra Ibrahim is undoubtedly one of the most talented traditional woodcarvers in the world.',
      videoUrl: 'assets/av/wood6.mp4',
      thumbnailUrl: 'assets/images/mudiao3_thumb.jpg',
      creator: mockArtisans[5],
      publishTime: DateTime.now().subtract(const Duration(days: 3)),
      likeCount: 678,
      viewCount: 3245,
      isLiked: true,
      tags: ['WoodCarver', 'Carving', 'Woodworking', 'Artisan'],
      difficulty: 'Medium',
      videoDuration: const Duration(seconds: 55),
    ),
    CarveBlueprint(
      id: 'post_007',
      title: 'My First Wood Carving Attempt',
      description: 'Learning from mistakes and celebrating small victories. Join me on this beginner\'s journey!',
      videoUrl: 'assets/av/wood7.mp4',
      thumbnailUrl: 'assets/images/mudiao1_thumb.jpg',
      creator: mockArtisans[6],
      publishTime: DateTime.now().subtract(const Duration(days: 4)),
      likeCount: 123,
      viewCount: 445,
      isLiked: false,
      tags: ['Beginner', 'Learning', 'First', 'Journey'],
      difficulty: 'Easy',
      videoDuration: const Duration(seconds: 32),
    ),

  ];
} 