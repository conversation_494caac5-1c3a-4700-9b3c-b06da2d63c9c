import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'GrainBlue/wu_grain_theme.dart';
import 'CarveStudio/wu_luxe_splash89.dart';
import 'CarveStudio/wu_main_navigator52.dart';
import 'WhittleLab/wu_blok_manger.dart';
import 'WhittleLab/wu_thumbil_genator.dart';
import 'WhittleLab/wu_craft_pst_mager.dart';
import 'WhittleLab/wu_vido_contller_manaer.dart';
import 'WhittleLab/wu_moshot_api.dart';
import 'SawdustArchive/wu_mock_data.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 设置iOS风格状态栏
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      statusBarBrightness: Brightness.dark,
    ),
  );
  
  // 初始化屏蔽管理器
  await WuBlockManager.instance.init();
  
  // 初始化缩略图生成器
  await WuThumbnailGenerator.instance.init();
  
  // 初始化用户帖子管理器
  await WuCraftPostManager.instance.init();

  // 初始化AI服务（这会在启动时申请网络权限）
  try {
    await WuMoonshotApi.instance.init();
    print('✅ AI服务初始化成功');
  } catch (e) {
    print('⚠️ AI服务初始化失败: $e');
  }

  // 在后台生成预设视频的缩略图
  _generatePresetThumbnails();
  
  runApp(const WuzoApp());
}

/// 生成预设视频的缩略图
void _generatePresetThumbnails() async {
  try {
    print('🎬 开始批量生成预设视频缩略图...');

    // 获取所有预设视频路径
    final videoPaths = WuMockData.mockCarvingPosts
        .map((post) => post.videoUrl)
        .toSet() // 去重
        .toList();

    print('📝 发现 ${videoPaths.length} 个预设视频需要生成缩略图');

    // 先清理无效缓存
    await WuThumbnailGenerator.instance.cleanupInvalidCache();

    // 在后台异步生成缩略图，添加延迟避免阻塞启动
    Future.delayed(const Duration(seconds: 2), () async {
      await WuThumbnailGenerator.instance.generatePresetThumbnails(videoPaths);

      // 生成完成后打印统计信息
      final stats = WuThumbnailGenerator.instance.getCacheStats();
      print('📊 缩略图缓存统计: $stats');
    });

  } catch (e) {
    print('⚠️ 生成预设缩略图时出错: $e');
  }
}

/// Wuzo木雕创作社区应用
class WuzoApp extends StatelessWidget {
  const WuzoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Wuzo - Wood Carving Community',
      debugShowCheckedModeBanner: false,
      theme: WuGrainTheme.themeData,
      home: const WuMainFlow(),
    );
  }
}

/// 主应用流程控制器
class WuMainFlow extends StatefulWidget {
  const WuMainFlow({super.key});

  @override
  State<WuMainFlow> createState() => _WuMainFlowState();

  /// 重新显示启动页（用于用户拒绝协议时）
  static void showSplashAgain() {
    _WuMainFlowState.showSplashAgain();
  }
}

class _WuMainFlowState extends State<WuMainFlow> {
  bool _showSplash = true;
  static _WuMainFlowState? _instance;

  @override
  void initState() {
    super.initState();
    _instance = this;
  }

  @override
  void dispose() {
    _instance = null;
    super.dispose();
  }

  void _onSplashComplete() {
    setState(() {
      _showSplash = false;
    });
  }

  /// 重新显示启动页（用于用户拒绝协议时）
  static void showSplashAgain() {
    _instance?.setState(() {
      _instance!._showSplash = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showSplash) {
      return WuLuxeSplash89(
        onAnimationComplete: _onSplashComplete,
        onGetStarted: _onSplashComplete,
      );
    }
    
    // 显示主导航器（包含底部导航栏）
    return const WuMainNavigator52();
  }
}
