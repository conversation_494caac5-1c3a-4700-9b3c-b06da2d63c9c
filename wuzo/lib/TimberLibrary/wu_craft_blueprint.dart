import 'package:equatable/equatable.dart';

/// 用户创建的作品帖子数据模型 - WuCraftPost
class WuCraftPost extends Equatable {
  final String id;
  final String title;
  final String content;
  final String authorId;
  final String authorName;
  final String authorAvatar;
  final String category;
  final String? videoPath;
  final String? thumbnailPath;
  final DateTime createTime;
  final int likeCount;
  final int viewCount;
  final bool isLiked;
  final bool isUserCreated;
  final Duration? videoDuration;

  const WuCraftPost({
    required this.id,
    required this.title,
    required this.content,
    required this.authorId,
    required this.authorName,
    required this.authorAvatar,
    required this.category,
    this.videoPath,
    this.thumbnailPath,
    required this.createTime,
    this.likeCount = 0,
    this.viewCount = 0,
    this.isLiked = false,
    this.isUserCreated = true,
    this.videoDuration,
  });

  /// 转换为CarveBlueprint以兼容现有视频流
  CarveBlueprint toCarveBlueprint() {
    return CarveBlueprint(
      id: id,
      title: title,
      description: content,
      videoUrl: videoPath ?? '',
      thumbnailUrl: thumbnailPath ?? '',
      creator: <PERSON><PERSON><PERSON><PERSON>(
        id: authorId,
        nickname: authorName,
        avatarUrl: authorAvatar,
        carveLevel: 1,
        isVerified: false,
      ),
      publishTime: createTime,
      likeCount: likeCount,
      viewCount: viewCount,
      isLiked: isLiked,
      tags: [category],
      difficulty: 'Medium',
      videoDuration: videoDuration ?? const Duration(seconds: 30),
    );
  }

  /// JSON序列化
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'authorId': authorId,
      'authorName': authorName,
      'authorAvatar': authorAvatar,
      'category': category,
      'videoPath': videoPath,
      'thumbnailPath': thumbnailPath,
      'createTime': createTime.millisecondsSinceEpoch,
      'likeCount': likeCount,
      'viewCount': viewCount,
      'isLiked': isLiked,
      'isUserCreated': isUserCreated,
      'videoDuration': videoDuration?.inSeconds,
    };
  }

  /// JSON反序列化
  factory WuCraftPost.fromJson(Map<String, dynamic> json) {
    return WuCraftPost(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      content: json['content'] ?? '',
      authorId: json['authorId'] ?? '',
      authorName: json['authorName'] ?? '',
      authorAvatar: json['authorAvatar'] ?? '',
      category: json['category'] ?? '',
      videoPath: json['videoPath'],
      thumbnailPath: json['thumbnailPath'],
      createTime: DateTime.fromMillisecondsSinceEpoch(json['createTime'] ?? 0),
      likeCount: json['likeCount'] ?? 0,
      viewCount: json['viewCount'] ?? 0,
      isLiked: json['isLiked'] ?? false,
      isUserCreated: json['isUserCreated'] ?? true,
      videoDuration: json['videoDuration'] != null 
          ? Duration(seconds: json['videoDuration']) 
          : null,
    );
  }

  WuCraftPost copyWith({
    String? id,
    String? title,
    String? content,
    String? authorId,
    String? authorName,
    String? authorAvatar,
    String? category,
    String? videoPath,
    String? thumbnailPath,
    DateTime? createTime,
    int? likeCount,
    int? viewCount,
    bool? isLiked,
    bool? isUserCreated,
    Duration? videoDuration,
  }) {
    return WuCraftPost(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      category: category ?? this.category,
      videoPath: videoPath ?? this.videoPath,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      createTime: createTime ?? this.createTime,
      likeCount: likeCount ?? this.likeCount,
      viewCount: viewCount ?? this.viewCount,
      isLiked: isLiked ?? this.isLiked,
      isUserCreated: isUserCreated ?? this.isUserCreated,
      videoDuration: videoDuration ?? this.videoDuration,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        content,
        authorId,
        authorName,
        authorAvatar,
        category,
        videoPath,
        thumbnailPath,
        createTime,
        likeCount,
        viewCount,
        isLiked,
        isUserCreated,
        videoDuration,
      ];
}

/// 木雕作品数据模型 - CarveBlueprint
class CarveBlueprint extends Equatable {
  final String id;
  final String title;
  final String description;
  final String videoUrl;
  final String thumbnailUrl;
  final WuArtisan creator;
  final DateTime publishTime;
  final int likeCount;
  final int viewCount;
  final bool isLiked;
  final List<String> tags;
  final String difficulty; // Easy, Medium, Hard
  final Duration videoDuration;

  const CarveBlueprint({
    required this.id,
    required this.title,
    required this.description,
    required this.videoUrl,
    required this.thumbnailUrl,
    required this.creator,
    required this.publishTime,
    this.likeCount = 0,
    this.viewCount = 0,
    this.isLiked = false,
    this.tags = const [],
    this.difficulty = 'Medium',
    this.videoDuration = const Duration(seconds: 30),
  });

  CarveBlueprint copyWith({
    String? id,
    String? title,
    String? description,
    String? videoUrl,
    String? thumbnailUrl,
    WuArtisan? creator,
    DateTime? publishTime,
    int? likeCount,
    int? viewCount,
    bool? isLiked,
    List<String>? tags,
    String? difficulty,
    Duration? videoDuration,
  }) {
    return CarveBlueprint(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      videoUrl: videoUrl ?? this.videoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      creator: creator ?? this.creator,
      publishTime: publishTime ?? this.publishTime,
      likeCount: likeCount ?? this.likeCount,
      viewCount: viewCount ?? this.viewCount,
      isLiked: isLiked ?? this.isLiked,
      tags: tags ?? this.tags,
      difficulty: difficulty ?? this.difficulty,
      videoDuration: videoDuration ?? this.videoDuration,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        videoUrl,
        thumbnailUrl,
        creator,
        publishTime,
        likeCount,
        viewCount,
        isLiked,
        tags,
        difficulty,
        videoDuration,
      ];
}

/// 工匠用户数据模型 - Artisan
class WuArtisan extends Equatable {
  final String id;
  final String nickname;
  final String avatarUrl;
  final String bio;
  final int followerCount;
  final int followingCount;
  final int carveLevel; // 1-10
  final List<String> specialties;
  final bool isVerified;

  const WuArtisan({
    required this.id,
    required this.nickname,
    required this.avatarUrl,
    this.bio = '',
    this.followerCount = 0,
    this.followingCount = 0,
    this.carveLevel = 1,
    this.specialties = const [],
    this.isVerified = false,
  });

  WuArtisan copyWith({
    String? id,
    String? nickname,
    String? avatarUrl,
    String? bio,
    int? followerCount,
    int? followingCount,
    int? carveLevel,
    List<String>? specialties,
    bool? isVerified,
  }) {
    return WuArtisan(
      id: id ?? this.id,
      nickname: nickname ?? this.nickname,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      bio: bio ?? this.bio,
      followerCount: followerCount ?? this.followerCount,
      followingCount: followingCount ?? this.followingCount,
      carveLevel: carveLevel ?? this.carveLevel,
      specialties: specialties ?? this.specialties,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  @override
  List<Object?> get props => [
        id,
        nickname,
        avatarUrl,
        bio,
        followerCount,
        followingCount,
        carveLevel,
        specialties,
        isVerified,
      ];
} 