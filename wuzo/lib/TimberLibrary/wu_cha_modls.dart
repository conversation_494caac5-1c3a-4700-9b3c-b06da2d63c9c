/// Wuzo聊天数据模型 - AI对话系统数据结构

/// 聊天角色枚举
enum WuChatRole {
  user,      // 用户
  assistant, // AI助手
  system,    // 系统
}

/// 聊天消息模型
class WuChatMessage {
  final String id;
  final String content;
  final WuChatRole role;
  final DateTime timestamp;

  const WuChatMessage({
    required this.id,
    required this.content,
    required this.role,
    required this.timestamp,
  });

  /// 从JSON创建实例
  factory WuChatMessage.fromJson(Map<String, dynamic> json) {
    return WuChatMessage(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      role: _parseRole(json['role']),
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'role': role.name,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  /// 解析角色字符串
  static WuChatRole _parseRole(String? roleString) {
    switch (roleString) {
      case 'user':
        return WuChatRole.user;
      case 'assistant':
        return WuChatRole.assistant;
      case 'system':
        return WuChatRole.system;
      default:
        return WuChatRole.user;
    }
  }

  /// 创建副本
  WuChatMessage copyWith({
    String? id,
    String? content,
    WuChatRole? role,
    DateTime? timestamp,
  }) {
    return WuChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      role: role ?? this.role,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  String toString() {
    return 'WuChatMessage(id: $id, role: $role, content: ${content.length > 50 ? "${content.substring(0, 50)}..." : content})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WuChatMessage &&
        other.id == id &&
        other.content == content &&
        other.role == role &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        content.hashCode ^
        role.hashCode ^
        timestamp.hashCode;
  }
}

/// 聊天会话模型
class WuChatSession {
  final String id;
  final String title;
  final DateTime createdAt;
  final DateTime lastMessageAt;
  final List<WuChatMessage> messages;

  const WuChatSession({
    required this.id,
    required this.title,
    required this.createdAt,
    required this.lastMessageAt,
    required this.messages,
  });

  /// 从JSON创建实例
  factory WuChatSession.fromJson(Map<String, dynamic> json) {
    return WuChatSession(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      createdAt: DateTime.parse(json['createdAt']),
      lastMessageAt: DateTime.parse(json['lastMessageAt']),
      messages: (json['messages'] as List<dynamic>?)
          ?.map((m) => WuChatMessage.fromJson(m))
          .toList() ?? [],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'lastMessageAt': lastMessageAt.toIso8601String(),
      'messages': messages.map((m) => m.toJson()).toList(),
    };
  }

  /// 创建副本
  WuChatSession copyWith({
    String? id,
    String? title,
    DateTime? createdAt,
    DateTime? lastMessageAt,
    List<WuChatMessage>? messages,
  }) {
    return WuChatSession(
      id: id ?? this.id,
      title: title ?? this.title,
      createdAt: createdAt ?? this.createdAt,
      lastMessageAt: lastMessageAt ?? this.lastMessageAt,
      messages: messages ?? this.messages,
    );
  }

  /// 添加消息
  WuChatSession addMessage(WuChatMessage message) {
    return copyWith(
      messages: [...messages, message],
      lastMessageAt: message.timestamp,
    );
  }

  /// 获取最后一条消息
  WuChatMessage? get lastMessage {
    return messages.isNotEmpty ? messages.last : null;
  }

  /// 获取消息数量
  int get messageCount => messages.length;

  @override
  String toString() {
    return 'WuChatSession(id: $id, title: $title, messageCount: $messageCount)';
  }
}

/// AI助手配置模型
class WuAiAssistantConfig {
  final String model;
  final double temperature;
  final int maxTokens;
  final String systemPrompt;

  const WuAiAssistantConfig({
    required this.model,
    required this.temperature,
    required this.maxTokens,
    required this.systemPrompt,
  });

  /// 默认配置
  factory WuAiAssistantConfig.defaultConfig() {
    return const WuAiAssistantConfig(
      model: 'moonshot-v1-8k',
      temperature: 0.7,
      maxTokens: 2000,
      systemPrompt: '你是一位专业的木雕艺术大师和教学专家。',
    );
  }

  /// 从JSON创建实例
  factory WuAiAssistantConfig.fromJson(Map<String, dynamic> json) {
    return WuAiAssistantConfig(
      model: json['model'] ?? 'moonshot-v1-8k',
      temperature: (json['temperature'] ?? 0.7).toDouble(),
      maxTokens: json['maxTokens'] ?? 2000,
      systemPrompt: json['systemPrompt'] ?? '',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'model': model,
      'temperature': temperature,
      'maxTokens': maxTokens,
      'systemPrompt': systemPrompt,
    };
  }
} 