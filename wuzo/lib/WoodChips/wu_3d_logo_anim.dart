import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import '../GrainBlue/wu_grain_theme.dart';

/// Wuzo 3D Logo动画组件 - 奢华木雕风格
class Wu3DLogoAnim extends StatefulWidget {
  final double size;
  final Duration duration;
  final bool autoStart;
  
  const Wu3DLogoAnim({
    super.key,
    this.size = 120,
    this.duration = const Duration(seconds: 3),
    this.autoStart = true,
  });

  @override
  State<Wu3DLogoAnim> createState() => _Wu3DLogoAnimState();
}

class _Wu3DLogoAnimState extends State<Wu3DLogoAnim>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _glowController;
  
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    
    // 3D旋转动画控制器
    _rotationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    // 缩放脉冲控制器
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // 发光效果控制器
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    
    // 3D旋转动画（Y轴旋转 + X轴轻微倾斜）
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.easeInOutCubic,
    ));
    
    // 缩放脉冲动画
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    // 发光强度动画
    _glowAnimation = Tween<double>(
      begin: 0.3,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOutSine,
    ));
    
    if (widget.autoStart) {
      _startAnimation();
    }
  }

  void _startAnimation() {
    // 启动3D旋转动画
    _rotationController.forward();
    
    // 延迟启动缩放脉冲
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _scaleController.repeat(reverse: true);
      }
    });
    
    // 延迟启动发光效果
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) {
        _glowController.repeat(reverse: true);
      }
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _rotationAnimation,
        _scaleAnimation,
        _glowAnimation,
      ]),
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Transform(
            alignment: Alignment.center,
            transform: Matrix4.identity()
              ..setEntry(3, 2, 0.001) // 透视效果
              ..rotateY(_rotationAnimation.value) // Y轴旋转
              ..rotateX(math.sin(_rotationAnimation.value) * 0.2), // X轴轻微摆动
            child: Container(
              width: widget.size,
              height: widget.size,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  center: Alignment.topLeft,
                  radius: 1.5,
                  colors: [
                    WuGrainTheme.canvasWhiteColor.withOpacity(_glowAnimation.value),
                    WuGrainTheme.walnutCarveColor.withOpacity(0.9),
                    WuGrainTheme.toolSteelColor.withOpacity(0.8),
                  ],
                  stops: const [0.0, 0.6, 1.0],
                ),
                boxShadow: [
                  // 内发光效果
                  BoxShadow(
                    color: WuGrainTheme.walnutCarveColor.withOpacity(_glowAnimation.value * 0.6),
                    blurRadius: 20 * _glowAnimation.value,
                    spreadRadius: 2 * _glowAnimation.value,
                  ),
                  // 外发光效果
                  BoxShadow(
                    color: WuGrainTheme.canvasWhiteColor.withOpacity(_glowAnimation.value * 0.4),
                    blurRadius: 30 * _glowAnimation.value,
                    spreadRadius: 5 * _glowAnimation.value,
                  ),
                ],
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 背景纹理
                  Container(
                    width: widget.size * 0.8,
                    height: widget.size * 0.8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          WuGrainTheme.pineWoodColor.withOpacity(0.3),
                          WuGrainTheme.walnutCarveColor.withOpacity(0.6),
                        ],
                      ),
                    ),
                  ),
                  // 中心"W"字母
                  Transform.rotate(
                    angle: _rotationAnimation.value * 0.5, // 字母反向旋转
                    child: Text(
                      'W',
                      style: TextStyle(
                        fontSize: widget.size * 0.4,
                        fontWeight: FontWeight.bold,
                        color: WuGrainTheme.canvasWhiteColor.withOpacity(_glowAnimation.value),
                        shadows: [
                          Shadow(
                            color: WuGrainTheme.charcoalGrayColor.withOpacity(0.8),
                            offset: const Offset(2, 2),
                            blurRadius: 4,
                          ),
                          Shadow(
                            color: WuGrainTheme.walnutCarveColor.withOpacity(_glowAnimation.value),
                            offset: const Offset(0, 0),
                            blurRadius: 10,
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 装饰性木纹线条
                  for (int i = 0; i < 6; i++)
                    Transform.rotate(
                      angle: (i * math.pi / 3) + (_rotationAnimation.value * 0.3),
                      child: Container(
                        width: 2,
                        height: widget.size * 0.3,
                        margin: EdgeInsets.only(top: widget.size * 0.15),
                        decoration: BoxDecoration(
                          color: WuGrainTheme.toolSteelColor.withOpacity(0.4 * _glowAnimation.value),
                          borderRadius: BorderRadius.circular(1),
                          boxShadow: [
                            BoxShadow(
                              color: WuGrainTheme.canvasWhiteColor.withOpacity(_glowAnimation.value * 0.3),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
} 