import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../GrainBlue/wu_grain_theme.dart';

/// Wuzo发光文字组件 - 奢华打字机效果
class WuGlowText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final Duration duration;
  final Duration? delay;
  final bool enableTypewriter;
  final bool enableGlow;
  final Color? glowColor;
  
  const WuGlowText({
    super.key,
    required this.text,
    this.style,
    this.duration = const Duration(milliseconds: 2000),
    this.delay,
    this.enableTypewriter = true,
    this.enableGlow = true,
    this.glowColor,
  });

  @override
  State<WuGlowText> createState() => _WuGlowTextState();
}

class _WuGlowTextState extends State<WuGlowText>
    with TickerProviderStateMixin {
  late AnimationController _typewriterController;
  late AnimationController _glowController;
  late Animation<int> _typewriterAnimation;
  late Animation<double> _glowAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // 打字机效果控制器
    _typewriterController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    // 发光效果控制器
    _glowController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    // 打字机动画
    _typewriterAnimation = IntTween(
      begin: 0,
      end: widget.text.length,
    ).animate(CurvedAnimation(
      parent: _typewriterController,
      curve: Curves.easeOut,
    ));
    
    // 发光强度动画
    _glowAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowController,
      curve: Curves.easeInOutSine,
    ));
    
    _startAnimation();
  }
  
  void _startAnimation() async {
    // 延迟启动（如果指定）
    if (widget.delay != null) {
      await Future.delayed(widget.delay!);
    }
    
    if (mounted) {
      // 启动打字机效果
      if (widget.enableTypewriter) {
        _typewriterController.forward();
      } else {
        _typewriterController.value = 1.0;
      }
      
      // 启动发光效果
      if (widget.enableGlow) {
        _glowController.repeat(reverse: true);
      }
    }
  }

  @override
  void dispose() {
    _typewriterController.dispose();
    _glowController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final defaultStyle = Theme.of(context).textTheme.headlineLarge?.copyWith(
      color: WuGrainTheme.canvasWhiteColor,
      fontWeight: FontWeight.bold,
    );
    
    final textStyle = widget.style ?? defaultStyle;
    final glowColor = widget.glowColor ?? WuGrainTheme.walnutCarveColor;
    
    return AnimatedBuilder(
      animation: Listenable.merge([
        _typewriterAnimation,
        _glowAnimation,
      ]),
      builder: (context, child) {
        final displayText = widget.enableTypewriter
            ? widget.text.substring(0, _typewriterAnimation.value)
            : widget.text;
        
        // 添加光标效果（仅在打字机模式下）
        final showCursor = widget.enableTypewriter && 
            _typewriterAnimation.value < widget.text.length;
        final finalText = showCursor ? '$displayText|' : displayText;
        
        return ShaderMask(
          shaderCallback: (bounds) {
            return LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                WuGrainTheme.canvasWhiteColor,
                glowColor.withOpacity(0.8),
                WuGrainTheme.toolSteelColor.withOpacity(0.6),
              ],
              stops: const [0.0, 0.5, 1.0],
            ).createShader(bounds);
          },
          child: Stack(
            children: [
              // 发光效果背景层
              if (widget.enableGlow) ...[
                // 外层大范围发光
                Text(
                  finalText,
                  style: textStyle?.copyWith(
                    color: glowColor.withOpacity(_glowAnimation.value * 0.3),
                    shadows: [
                      Shadow(
                        color: glowColor.withOpacity(_glowAnimation.value * 0.6),
                        blurRadius: 25,
                      ),
                      Shadow(
                        color: WuGrainTheme.canvasWhiteColor.withOpacity(_glowAnimation.value * 0.4),
                        blurRadius: 15,
                      ),
                    ],
                  ),
                ),
                // 中层中等发光
                Text(
                  finalText,
                  style: textStyle?.copyWith(
                    color: glowColor.withOpacity(_glowAnimation.value * 0.6),
                    shadows: [
                      Shadow(
                        color: glowColor.withOpacity(_glowAnimation.value * 0.8),
                        blurRadius: 12,
                      ),
                    ],
                  ),
                ),
              ],
              // 主文字层
              Text(
                finalText,
                style: textStyle?.copyWith(
                  color: Colors.white,
                  shadows: [
                    // 基础阴影
                    Shadow(
                      color: WuGrainTheme.charcoalGrayColor.withOpacity(0.8),
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                    ),
                    // 发光阴影
                    if (widget.enableGlow)
                      Shadow(
                        color: glowColor.withOpacity(_glowAnimation.value * 0.8),
                        blurRadius: 8,
                      ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

/// 多行发光文字组件
class WuGlowTextMultiline extends StatelessWidget {
  final List<String> lines;
  final TextStyle? style;
  final Duration lineDuration;
  final Duration lineDelay;
  final bool enableTypewriter;
  final bool enableGlow;
  final Color? glowColor;
  
  const WuGlowTextMultiline({
    super.key,
    required this.lines,
    this.style,
    this.lineDuration = const Duration(milliseconds: 1500),
    this.lineDelay = const Duration(milliseconds: 300),
    this.enableTypewriter = true,
    this.enableGlow = true,
    this.glowColor,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: lines.asMap().entries.map((entry) {
        final index = entry.key;
        final line = entry.value;
        
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: WuGlowText(
            text: line,
            style: style,
            duration: lineDuration,
            delay: Duration(milliseconds: (lineDelay.inMilliseconds * index)),
            enableTypewriter: enableTypewriter,
            enableGlow: enableGlow,
            glowColor: glowColor,
          ),
        );
      }).toList(),
    );
  }
} 