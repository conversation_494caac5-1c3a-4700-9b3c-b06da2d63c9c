import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../GrainBlue/wu_grain_theme.dart';

/// Wuzo高级底部导航栏 - Luxury iOS Design System
class WuGrainNavBar extends StatefulWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<WuNavItem> items;
  
  const WuGrainNavBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  });

  @override
  State<WuGrainNavBar> createState() => _WuGrainNavBarState();
}

class _WuGrainNavBarState extends State<WuGrainNavBar>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<double>> _glowAnimations;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.items.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 1000),
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.8, end: 1.2).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    _glowAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // 激活当前选中项的动画
    if (widget.currentIndex < _controllers.length) {
      _controllers[widget.currentIndex].forward();
    }
  }

  void _onItemTapped(int index) {
    // 触觉反馈
    HapticFeedback.lightImpact();
    
    // 重置所有动画
    for (var controller in _controllers) {
      controller.reset();
    }
    
    // 播放选中项的闪烁动画
    _controllers[index].forward().then((_) {
      // 动画完成后保持选中状态
      _controllers[index].reset();
      _controllers[index].forward();
    });
    
    // 回调父组件
    widget.onTap(index);
  }

  @override
  void didUpdateWidget(WuGrainNavBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentIndex != widget.currentIndex) {
      // 重置所有动画
      for (var controller in _controllers) {
        controller.reset();
      }
      // 激活新选中项
      if (widget.currentIndex < _controllers.length) {
        _controllers[widget.currentIndex].forward();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        // 毛玻璃效果背景
        color: WuGrainTheme.canvasWhiteColor.withOpacity(0.95),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        border: Border.all(
          color: WuGrainTheme.pineWoodColor.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          // 上方阴影
          BoxShadow(
            color: WuGrainTheme.charcoalGrayColor.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -8),
            spreadRadius: 0,
          ),
          // 强调阴影
          BoxShadow(
            color: WuGrainTheme.walnutCarveColor.withOpacity(0.05),
            blurRadius: 30,
            offset: const Offset(0, -5),
            spreadRadius: 2,
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Container(
          height: 70,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(widget.items.length, (index) {
              final item = widget.items[index];
              final isSelected = index == widget.currentIndex;
              
              return Expanded(
                child: AnimatedBuilder(
                  animation: Listenable.merge([
                    _scaleAnimations[index],
                    _glowAnimations[index],
                  ]),
                  builder: (context, child) {
                    return GestureDetector(
                      onTap: () => _onItemTapped(index),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // 图标容器
                            Transform.scale(
                              scale: isSelected 
                                  ? _scaleAnimations[index].value 
                                  : 1.0,
                              child: Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  // 选中时的背景渐变
                                  gradient: isSelected
                                      ? RadialGradient(
                                          colors: [
                                            WuGrainTheme.walnutCarveColor.withOpacity(
                                              0.2 + (_glowAnimations[index].value * 0.3)
                                            ),
                                            WuGrainTheme.walnutCarveColor.withOpacity(
                                              0.1 + (_glowAnimations[index].value * 0.2)
                                            ),
                                          ],
                                        )
                                      : null,
                                  // 发光效果
                                  boxShadow: isSelected
                                      ? [
                                          BoxShadow(
                                            color: WuGrainTheme.walnutCarveColor.withOpacity(
                                              _glowAnimations[index].value * 0.6
                                            ),
                                            blurRadius: 15 * _glowAnimations[index].value,
                                            spreadRadius: 2 * _glowAnimations[index].value,
                                          ),
                                          BoxShadow(
                                            color: WuGrainTheme.canvasWhiteColor.withOpacity(
                                              _glowAnimations[index].value * 0.4
                                            ),
                                            blurRadius: 8 * _glowAnimations[index].value,
                                          ),
                                        ]
                                      : null,
                                ),
                                child: Icon(
                                  isSelected ? item.selectedIcon : item.icon,
                                  size: 22,
                                  color: isSelected
                                      ? WuGrainTheme.walnutCarveColor
                                      : WuGrainTheme.toolSteelColor,
                                ),
                              ),
                            ),
                            
                            const SizedBox(height: 2),
                            
                            // 标签文字
                            Text(
                              item.label,
                              style: TextStyle(
                                fontSize: 9,
                                fontWeight: isSelected 
                                    ? FontWeight.w600 
                                    : FontWeight.w400,
                                color: isSelected
                                    ? WuGrainTheme.walnutCarveColor
                                    : WuGrainTheme.toolSteelColor,
                                letterSpacing: 0.3,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              );
            }),
          ),
        ),
      ),
    );
  }
}

/// 导航栏项目数据模型
class WuNavItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  
  const WuNavItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
  });
}

/// 预定义的导航项目
class WuNavItems {
  // 主页 - 使用木纹图标
  static const WuNavItem timber = WuNavItem(
    icon: CupertinoIcons.square_stack_3d_down_right,
    selectedIcon: CupertinoIcons.square_stack_3d_down_right_fill,
    label: 'Craft',
  );
  
  // 发布 - 使用雕刻工具图标
  static const WuNavItem forge = WuNavItem(
    icon: CupertinoIcons.hammer,
    selectedIcon: CupertinoIcons.hammer_fill,
    label: 'Create',
  );
  
  // 个人中心 - 使用工匠头像图标
  static const WuNavItem artisan = WuNavItem(
    icon: CupertinoIcons.person_crop_square,
    selectedIcon: CupertinoIcons.person_crop_square_fill,
    label: 'Artisan',
  );
}

/// 特殊的发布按钮组件 - 中央突出设计
class WuForgeButton extends StatefulWidget {
  final VoidCallback onTap;
  final bool isActive;
  
  const WuForgeButton({
    super.key,
    required this.onTap,
    this.isActive = false,
  });

  @override
  State<WuForgeButton> createState() => _WuForgeButtonState();
}

class _WuForgeButtonState extends State<WuForgeButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.25, // 90度旋转
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOutBack,
    ));
  }

  void _handleTap() {
    HapticFeedback.mediumImpact();
    
    // 播放按钮动画
    _controller.forward().then((_) {
      _controller.reverse();
    });
    
    widget.onTap();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return GestureDetector(
          onTap: _handleTap,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: Container(
                width: 56,
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      WuGrainTheme.walnutCarveColor,
                      WuGrainTheme.toolSteelColor,
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    // 主阴影
                    BoxShadow(
                      color: WuGrainTheme.walnutCarveColor.withOpacity(0.4),
                      blurRadius: 20,
                      spreadRadius: 2,
                      offset: const Offset(0, 4),
                    ),
                    // 发光效果
                    BoxShadow(
                      color: WuGrainTheme.canvasWhiteColor.withOpacity(0.6),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  CupertinoIcons.add,
                  size: 28,
                  color: WuGrainTheme.canvasWhiteColor,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
} 