import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:video_player/video_player.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../GrainBlue/wu_grain_theme.dart';
import '../TimberLibrary/wu_craft_blueprint.dart';
import '../WhittleLab/wu_thumbil_genator.dart';
import '../WhittleLab/wu_vido_contller_manaer.dart';

/// Wuzo视频卡片组件 - 木雕作品展示
class WuVideoCard extends StatefulWidget {
  final CarveBlueprint post;
  final VoidCallback? onLike;
  final VoidCallback? onDetailTap;
  final VoidCallback? onAvatarTap;
  final VoidCallback? onBlockPost;
  final VoidCallback? onReportPost;
  final VoidCallback? onDeletePost;
  final bool autoPlay;
  final bool isUserPost;
  
  const WuVideoCard({
    super.key,
    required this.post,
    this.onLike,
    this.onDetailTap,
    this.onAvatarTap,
    this.onBlockPost,
    this.onReportPost,
    this.onDeletePost,
    this.autoPlay = false,
    this.isUserPost = false,
  });

  @override
  State<WuVideoCard> createState() => _WuVideoCardState();
}

class _WuVideoCardState extends State<WuVideoCard> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  bool _isPlaying = false;
  Duration _currentPosition = Duration.zero;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  @override
  void didUpdateWidget(WuVideoCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 当autoPlay状态变化时，控制视频播放
    if (oldWidget.autoPlay != widget.autoPlay) {
      if (widget.autoPlay && !_isPlaying) {
        _playVideo();
      } else if (!widget.autoPlay && _isPlaying) {
        _pauseVideo();
      }
    }

    // 检查全局控制器的播放状态，同步本地状态
    if (_isInitialized) {
      final isGloballyPlaying = WuVideoControllerManager.instance.isVideoPlaying(widget.post.id);
      if (isGloballyPlaying != _isPlaying) {
        setState(() {
          _isPlaying = isGloballyPlaying;
        });
      }
    }
  }

  void _initializeVideo() async {
    if (widget.post.videoUrl.isEmpty) return;
    
    _controller = VideoPlayerController.asset(widget.post.videoUrl);
    
    try {
      await _controller.initialize();
      _controller.setLooping(true);

      // 添加位置监听器
      _controller.addListener(_onVideoPositionChanged);

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });

        // 注册到全局视频控制器管理器
        WuVideoControllerManager.instance.registerController(widget.post.id, _controller);

        if (widget.autoPlay) {
          _playVideo();
        }
      }
    } catch (e) {
      // 对于用户上传的视频，使用文件路径
      if (widget.isUserPost && widget.post.videoUrl.isNotEmpty) {
        try {
          _controller = VideoPlayerController.file(File(widget.post.videoUrl));
          await _controller.initialize();
          _controller.setLooping(true);

          // 添加位置监听器
          _controller.addListener(_onVideoPositionChanged);

          if (mounted) {
            setState(() {
              _isInitialized = true;
            });

            // 注册到全局视频控制器管理器
            WuVideoControllerManager.instance.registerController(widget.post.id, _controller);

            if (widget.autoPlay) {
              _playVideo();
            }
          }
        } catch (fileError) {
          print('用户视频初始化失败: $fileError');
        }
      } else {
        print('预设视频初始化失败: $e');
      }
    }
  }

  void _playVideo() {
    if (_isInitialized && mounted) {
      // 使用全局视频控制器管理器播放（会自动暂停其他视频）
      WuVideoControllerManager.instance.playVideo(widget.post.id);
      setState(() {
        _isPlaying = true;
      });
    }
  }

  void _pauseVideo() {
    if (_isInitialized && mounted) {
      WuVideoControllerManager.instance.pauseVideo(widget.post.id);
      setState(() {
        _isPlaying = false;
      });
    }
  }

  void _togglePlay() {
    if (_isPlaying) {
      _pauseVideo();
    } else {
      _playVideo();
    }
  }

  /// 视频位置变化监听器
  void _onVideoPositionChanged() {
    if (_controller.value.isInitialized && mounted) {
      final newPosition = _controller.value.position;
      if (newPosition != _currentPosition) {
        setState(() {
          _currentPosition = newPosition;
        });
      }
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes);
    final seconds = twoDigits(duration.inSeconds % 60);
    return '$minutes:$seconds';
  }

  String _formatCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    }
    return count.toString();
  }

  @override
  void dispose() {
    // 移除位置监听器
    if (_isInitialized) {
      _controller.removeListener(_onVideoPositionChanged);
    }
    // 从全局视频控制器管理器注销
    WuVideoControllerManager.instance.unregisterController(widget.post.id);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: WuGrainTheme.canvasWhiteColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: WuGrainTheme.mediumShadow,
        // 用户帖子特殊边框
        border: widget.isUserPost
            ? Border.all(
                color: WuGrainTheme.walnutCarveColor.withOpacity(0.3),
                width: 2,
              )
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用户帖子标识
          if (widget.isUserPost) _buildUserPostBadge(),
          
          // 用户信息头部
          _buildUserHeader(),
          
          // 视频播放区域
          _buildVideoPlayer(),
          
          // 交互按钮区域
          _buildActionButtons(),
          
          // 作品信息
          _buildPostInfo(),
        ],
      ),
    );
  }

  /// 构建用户帖子标识
  Widget _buildUserPostBadge() {
    return Container(
      margin: const EdgeInsets.all(12),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            WuGrainTheme.walnutCarveColor,
            WuGrainTheme.toolSteelColor,
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            CupertinoIcons.star_fill,
            size: 12,
            color: WuGrainTheme.canvasWhiteColor,
          ),
          const SizedBox(width: 4),
          const Text(
            'Your Craft',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: WuGrainTheme.canvasWhiteColor,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建用户信息头部
  Widget _buildUserHeader() {
    return Padding(
      padding: EdgeInsets.fromLTRB(16, widget.isUserPost ? 0 : 16, 16, 16),
      child: Row(
        children: [
          // 用户头像
          GestureDetector(
            onTap: widget.onAvatarTap,
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: WuGrainTheme.lightShadow,
              ),
              child: ClipOval(
                child: _buildAvatarImage(),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      widget.post.creator.nickname,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: WuGrainTheme.charcoalGrayColor,
                      ),
                    ),
                    if (widget.post.creator.isVerified) ...[
                      const SizedBox(width: 4),
                      Icon(
                        CupertinoIcons.checkmark_seal_fill,
                        size: 16,
                        color: WuGrainTheme.walnutCarveColor,
                      ),
                    ],
                  ],
                ),
                Text(
                  'Level ${widget.post.creator.carveLevel} • ${widget.post.difficulty}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: WuGrainTheme.toolSteelColor,
                  ),
                ),
              ],
            ),
          ),
          
          // 更多操作按钮
          GestureDetector(
            onTap: _showMoreOptions,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: WuGrainTheme.pineWoodColor.withOpacity(0.5),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                CupertinoIcons.ellipsis,
                color: WuGrainTheme.toolSteelColor,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建头像图片
  Widget _buildAvatarImage() {
    if (widget.post.creator.avatarUrl.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: widget.post.creator.avatarUrl,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: WuGrainTheme.pineWoodColor,
          child: Icon(
            CupertinoIcons.person,
            color: WuGrainTheme.toolSteelColor,
            size: 20,
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: WuGrainTheme.walnutCarveColor,
          child: Icon(
            CupertinoIcons.person,
            color: WuGrainTheme.canvasWhiteColor,
            size: 20,
          ),
        ),
      );
    } else if (widget.post.creator.avatarUrl.startsWith('/')) {
      // 本地文件路径
      return Image.file(
        File(widget.post.creator.avatarUrl),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    } else {
      // Assets路径
      return Image.asset(
        widget.post.creator.avatarUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultAvatar();
        },
      );
    }
  }

  /// 构建默认头像
  Widget _buildDefaultAvatar() {
    return Container(
      color: WuGrainTheme.walnutCarveColor,
      child: Icon(
        CupertinoIcons.person,
        color: WuGrainTheme.canvasWhiteColor,
        size: 20,
      ),
    );
  }

  void _showMoreOptions() {
    final List<Widget> actions = [];
    
    // 用户帖子显示删除选项
    if (widget.isUserPost && widget.onDeletePost != null) {
      actions.add(
        CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
            widget.onDeletePost!();
          },
          child: Text(
            'Delete Post',
            style: TextStyle(color: WuGrainTheme.errorRedWood),
          ),
        ),
      );
    } else {
      // 预设帖子显示屏蔽和举报选项
      if (widget.onBlockPost != null) {
        actions.add(
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onBlockPost!();
            },
            child: const Text('Block Post'),
          ),
        );
      }
      
      if (widget.onReportPost != null) {
        actions.add(
          CupertinoActionSheetAction(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onReportPost!();
            },
            child: Text(
              'Report Post',
              style: TextStyle(color: WuGrainTheme.errorRedWood),
            ),
          ),
        );
      }
    }

    if (actions.isEmpty) return;

    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoActionSheet(
        title: Text(widget.isUserPost ? 'Your Post Options' : 'Post Options'),
        actions: actions,
        cancelButton: CupertinoActionSheetAction(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
      ),
    );
  }

  /// 构建视频播放器
  Widget _buildVideoPlayer() {
    return GestureDetector(
      onTap: _togglePlay,
      child: Container(
        height: 300,
        margin: const EdgeInsets.symmetric(horizontal: 16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: WuGrainTheme.charcoalGrayColor,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            alignment: Alignment.center,
            children: [
              if (_isInitialized)
                AspectRatio(
                  aspectRatio: _controller.value.aspectRatio,
                  child: VideoPlayer(_controller),
                )
              else
                // 未初始化时显示缩略图或占位图
                _buildVideoPlaceholder(),
              
              // 播放/暂停按钮
              if (_isInitialized && !_isPlaying)
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: _togglePlay,
                    icon: Icon(
                      CupertinoIcons.play_fill,
                      color: WuGrainTheme.canvasWhiteColor,
                      size: 32,
                    ),
                  ),
                ),
              
              // 视频时长
              if (_isInitialized)
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.7),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _formatDuration(_currentPosition),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建视频占位符
  Widget _buildVideoPlaceholder() {
    // 尝试显示缩略图
    if (widget.isUserPost && widget.post.thumbnailUrl?.isNotEmpty == true) {
      // 检查是否是占位符文件（模拟器或生成失败）
      if (widget.post.thumbnailUrl!.endsWith('_placeholder.txt') || 
          widget.post.thumbnailUrl!.endsWith('_simulator.txt')) {
        return _buildDefaultVideoPlaceholder();
      }
      
      return Image.file(
        File(widget.post.thumbnailUrl!),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildDefaultVideoPlaceholder();
        },
      );
    } else if (!widget.isUserPost) {
      return FutureBuilder<String?>(
        future: Future.value(WuThumbnailGenerator.instance.getThumbnailPath(widget.post.videoUrl)),
        builder: (context, snapshot) {
          final thumbnailPath = snapshot.data;
          
          if (thumbnailPath != null) {
            // 检查是否是占位符文件
            if (thumbnailPath.endsWith('_placeholder.txt') || 
                thumbnailPath.endsWith('_simulator.txt')) {
              return _buildDefaultVideoPlaceholder();
            }
            
            if (File(thumbnailPath).existsSync()) {
              return Image.file(
                File(thumbnailPath),
                width: double.infinity,
                height: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return _buildDefaultVideoPlaceholder();
                },
              );
            }
          }
          return _buildDefaultVideoPlaceholder();
        },
      );
    }
    
    return _buildDefaultVideoPlaceholder();
  }

  /// 构建交互按钮
  Widget _buildActionButtons() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          // 点赞按钮
          _buildActionButton(
            icon: widget.post.isLiked 
                ? CupertinoIcons.heart_fill 
                : CupertinoIcons.heart,
            label: _formatCount(widget.post.likeCount),
            isActive: widget.post.isLiked,
            onTap: widget.onLike,
          ),
          
          // 观看数
          _buildActionButton(
            icon: CupertinoIcons.eye,
            label: _formatCount(widget.post.viewCount),
            isActive: false,
            onTap: null,
          ),
          
          // // 分享按钮
          // _buildActionButton(
          //   icon: CupertinoIcons.share,
          //   label: 'Share',
          //   isActive: false,
          //   onTap: () {},
          // ),
          
          // 详情按钮
          _buildActionButton(
            icon: CupertinoIcons.info_circle,
            label: 'Details',
            isActive: false,
            onTap: widget.onDetailTap,
          ),
        ],
      ),
    );
  }

  /// 构建单个操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required bool isActive,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 24,
            color: isActive 
                ? WuGrainTheme.walnutCarveColor 
                : WuGrainTheme.toolSteelColor,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: isActive 
                  ? WuGrainTheme.walnutCarveColor 
                  : WuGrainTheme.toolSteelColor,
              fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建作品信息
  Widget _buildPostInfo() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          Text(
            widget.post.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: WuGrainTheme.charcoalGrayColor,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // 描述
          Text(
            widget.post.description,
            style: const TextStyle(
              fontSize: 14,
              color: WuGrainTheme.toolSteelColor,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          
          const SizedBox(height: 8),
          
          // 标签
          Wrap(
            spacing: 8,
            runSpacing: 6,
            children: widget.post.tags.take(3).map((tag) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: WuGrainTheme.pineWoodColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '#$tag',
                  style: const TextStyle(
                    fontSize: 12,
                    color: WuGrainTheme.toolSteelColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// 构建视频播放器默认占位图
  Widget _buildDefaultVideoPlaceholder() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: WuGrainTheme.charcoalGrayColor,
      child: Icon(
        CupertinoIcons.play_circle,
        size: 60,
        color: WuGrainTheme.canvasWhiteColor.withOpacity(0.8),
      ),
    );
  }
} 