#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wuzo头像下载脚本
预先下载用户头像到assets文件夹，避免在app启动时下载
"""

import os
import urllib.request
import urllib.parse
import json
import time
from pathlib import Path

# Unsplash API配置
ACCESS_KEY = 'sB7G-rHWgjtvcp1Ag78GATMnmDApT_WACkbWdygsgKM'
BASE_URL = 'https://api.unsplash.com'

# 头像文件列表（对应WuMockData中的用户）
AVATAR_LIST = [
    {'filename': 'avatar_chen.jpg', 'query': 'portrait face man craftsman'},
    {'filename': 'avatar_alex.jpg', 'query': 'portrait face man designer'},
    {'filename': 'avatar_sarah.jpg', 'query': 'portrait face woman artist'},
    {'filename': 'avatar_mike.jpg', 'query': 'portrait face man teacher'},
    {'filename': 'avatar_emma.jpg', 'query': 'portrait face woman nature'},
    {'filename': 'avatar_tom.jpg', 'query': 'portrait face man master'},
    {'filename': 'avatar_jin.jpg', 'query': 'portrait face young person'},
    {'filename': 'avatar_lisa.jpg', 'query': 'portrait face woman precision'},
]

def download_avatar(filename, query, size=200):
    """下载单个头像"""
    try:
        print(f"正在下载头像: {filename}")
        
        # 构建API请求URL
        params = {
            'query': query,
            'w': str(size),
            'h': str(size),
            'fit': 'crop',
            'crop': 'face',
        }
        
        url = f'{BASE_URL}/photos/random?' + urllib.parse.urlencode(params)
        
        # 创建请求
        request = urllib.request.Request(url)
        request.add_header('Authorization', f'Client-ID {ACCESS_KEY}')
        
        # 获取随机图片信息
        with urllib.request.urlopen(request) as response:
            if response.status == 200:
                data = json.loads(response.read().decode('utf-8'))
                image_url = data['urls']['regular']
                
                # 下载图片
                with urllib.request.urlopen(image_url) as img_response:
                    if img_response.status == 200:
                        return img_response.read()
                    else:
                        print(f"下载图片失败: {filename}, 状态码: {img_response.status}")
                        return None
            else:
                print(f"API请求失败: {filename}, 状态码: {response.status}")
                return None
                
    except Exception as e:
        print(f"下载头像出错: {filename}, 错误: {e}")
        return None

def main():
    """主函数"""
    # 确定项目根目录和assets目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent
    avatars_dir = project_root / 'assets' / 'avatars'
    
    # 创建avatars目录
    avatars_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"项目根目录: {project_root}")
    print(f"头像保存目录: {avatars_dir}")
    print(f"开始下载 {len(AVATAR_LIST)} 个头像...")
    
    success_count = 0
    
    for avatar_info in AVATAR_LIST:
        filename = avatar_info['filename']
        query = avatar_info['query']
        file_path = avatars_dir / filename
        
        # 检查文件是否已存在
        if file_path.exists():
            print(f"头像已存在，跳过: {filename}")
            success_count += 1
            continue
        
        # 下载头像
        image_data = download_avatar(filename, query)
        
        if image_data:
            # 保存文件
            with open(file_path, 'wb') as f:
                f.write(image_data)
            print(f"头像下载成功: {filename}")
            success_count += 1
        else:
            print(f"头像下载失败: {filename}")
        
        # 延迟避免API限制
        time.sleep(1)
    
    print(f"\n下载完成！成功: {success_count}/{len(AVATAR_LIST)}")
    
    # 更新pubspec.yaml确保assets被包含
    pubspec_path = project_root / 'pubspec.yaml'
    if pubspec_path.exists():
        with open(pubspec_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'assets/avatars/' not in content:
            print("正在更新pubspec.yaml以包含avatars资源...")
            # 在assets部分添加avatars目录
            updated_content = content.replace(
                '  assets:\n    - assets/av/\n    - assets/images/',
                '  assets:\n    - assets/av/\n    - assets/images/\n    - assets/avatars/'
            )
            
            with open(pubspec_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print("pubspec.yaml已更新")
        else:
            print("pubspec.yaml已包含avatars资源")

if __name__ == '__main__':
    main() 